scripts/
tests/
notebooks/

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
#.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini
$RECYCLE.BIN/

# Testing
.pytest_cache/
.coverage
htmlcov/
.tox/
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
examples/

# Logs
*.log
logs/

# Environment files
#.env.*
!.env.example

# Database files
*.sqlite
*.sqlite3
*.db
test_db.sqlite

# Vector stores and embeddings
chroma_db/
data/chroma_stores/
*.chroma

# Processed documents
data/processed/
data/raw/*/
!data/raw/README.md

# Temporary files
tmp/
temp/
.tmp/

# API keys and secrets
secrets/
*.key
*.pem

# Model files
*.pkl
*.joblib

# Large data files
*.csv
!configs/*.yaml
!configs/*.yml

# Backup files
*.bak
*.backup

# Cache
.cache/
cache/

# Jupyter
.ipynb_checkpoints/

# Node.js (if any)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Documentation builds
docs/_build/
/site

# Type checking
.mypy_cache/
.dmypy.json
dmypy.json
.pyre/
.pytype/

# Package management
pip-log.txt
pip-delete-this-directory.txt