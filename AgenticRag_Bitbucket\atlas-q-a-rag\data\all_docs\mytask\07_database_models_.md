# Chapter 7: Database Models

Welcome back to the `mytask` tutorial! So far, we've explored how users log in ([Chapter 1: User and Authentication System](01_user_and_authentication_system_.md)), how tasks are the main feature ([Chapter 2: Task Management Core](02_task_management_core_.md)), how the frontend manages data ([Chapter 3: Frontend State Management (Redux Toolkit)](03_frontend_state_management__redux_toolkit__.md)), how the frontend talks to the backend ([Chapter 4: API Communication (RTK Query)](04_api_communication__rtk_query__.md)), how the backend receives and processes requests ([Chapter 5: API Endpoints and Controllers (Backend)](05_api_endpoints_and_controllers__backend__.md)), and how we integrate with external systems like Atlas University ([Chapter 6: Atlas University Integration](06_atlas_university_integration_.md)).

All these features rely on one fundamental thing: **storing data**. Where do we keep the list of users? How do we save a task after it's created? What information do we store about each checklist item?

The answer is in our **Database**. In `mytask`, we use **MongoDB** as our database. MongoDB is a type of database that stores data in a flexible, document-like format (similar to JSON).

But even with a flexible database like MongoDB, it's important to have a defined structure for the data our application uses. We need to know, for example, that every user *must* have an email, or that a task should have a `title` which is text.

This is where **Database Models** come in.

### What are Database Models?

Imagine you're building something complex, like a house or a recipe. You need a **blueprint** or a **recipe card** that tells you exactly what goes into it, what type of materials or ingredients are needed, and how things fit together.

In our application, **Database Models** are like the **blueprints** or **recipes** for our data. They define:

1.  **What information** we expect to store for a specific type of data (like a User or a Task).
2.  **What type** that information should be (like text, a number, a date, true/false).
3.  Any **special rules** for that information (like if it's required, if it must be unique).
4.  How different pieces of data might be **linked** together (like linking a Task to the User who created it).

In Node.js applications that use MongoDB, we often use a library called **Mongoose**. Mongoose helps us work with MongoDB in a more structured way. It provides the tools to create these blueprints (called **Schemas**) and turn them into **Models** that our backend code can use to interact with the database.

So, a Mongoose **Schema** is the definition of the structure, and a **Model** is the actual object we use in our code to perform operations (like saving, finding, updating, deleting) on a specific type of data in the database.

### Our Goal: Understanding User and Task Data Structure

Let's use examples we've already seen: the **User** data and the **Task** data. When our backend processes an Atlas login ([Chapter 6: Atlas University Integration](06_atlas_university_integration_.md)) or creates a task ([Chapter 5: API Endpoints and Controllers (Backend)](05_api_endpoints_and_controllers__backend__.md)), it needs to know how to store and retrieve this information correctly.

Database Models provide that necessary structure.

### Using Models in Backend Code

As we saw in previous chapters, backend **Controllers** ([API Endpoints and Controllers (Backend)](05_api_endpoints_and_controllers__backend__.md)) are responsible for handling requests. A key part of this is interacting with the database using the **Models**.

Let's revisit snippets from the `atlasAuthController.js` and `taskController.js` to see models in action.

From the Atlas login controller ([Chapter 6](06_atlas_university_integration_.md)):

```javascript
// server\controllers\atlasAuthController.js (Snippet)
import User from "../models/userModel.js"; // Import the User Model

// ... inside processAtlasLogin function ...

// Find existing user using Atlas ID, GUID, or username/email
let user = await User.findOne({ // Using the User Model's findOne method
    $or: [{ atlasUserId: userId }, { guid: userData.guid }, { username: samAccountName }, { email: email }]
});

if (user) {
  // Update fields and save using the user object instance
  user.name = name || user.name;
  // ... update other fields ...
  await user.save({ validateBeforeSave: false }); // Using save method on the model instance
} else {
  // Create new user using the User Model's create method
  user = await User.create({
    name: name || 'New Atlas User',
    email: email || `${samAccountName}@atlas.edu.tr`,
    // ... other user fields ...
    atlasUserId: userId,
    guid: userData.guid,
    // ...
  });
}

// ... rest of the controller ...
```
*   Notice `import User from "../models/userModel.js";`. This brings our `User` model into the controller.
*   `User.findOne({...})` is a method provided by the Mongoose `User` model. It searches the database for a document (a user record) that matches the criteria inside `{}`.
*   `await user.save()` is a method on a specific `user` object *instance* we found. It saves any changes made to that object back to the database.
*   `await User.create({...})` is another method on the `User` model. It creates a *new* document in the database using the data provided in `{}` and returns the newly created user object.

These methods (`findOne`, `save`, `create`, etc., along with others like `find`, `findById`, `updateOne`, `deleteOne`) are how our backend code interacts with the database, all thanks to the structure and tools provided by the Mongoose Model.

Now, let's see the Task model being used in the task creation controller ([Chapter 5](05_api_endpoints_and_controllers__backend__.md)):

```javascript
// server\controllers\taskController.js (Snippet)
import Task from "../models/taskModel.js"; // Import the Task Model

// ... inside createTask function ...

const taskData = {
  title,
  description: description || "",
  // ... other task fields ...
  createdBy: userId, // Associate task with the logged-in user's ID
  parent: parentId || null,
};

console.log('Attempting to save task data:', taskData);

// Use the Task Model's create method to save a new task document
const task = await Task.create(taskData);

console.log('New task created in DB with ID:', task._id);

// ... send response ...
```
*   Similarly, we import `Task` from `taskModel.js`.
*   `await Task.create(taskData)` uses the `create` method on the `Task` model to save the new task information to the database, following the structure defined in the `Task` model's schema.

So, the backend controllers rely heavily on these models to know *how* to talk to the database and what the data should look like.

### Defining the Models (Behind the Scenes)

Now let's look at the blueprints themselves – the **Schemas** and how they become **Models**. These are defined in files like `server\models\userModel.js`, `server\models\taskModel.js`, and `server\models\checklistModel.js`.

Let's look at a simplified `userModel.js`:

```javascript
// server\models\userModel.js (Simplified Schema Definition)
import mongoose, { Schema } from "mongoose"; // Import Mongoose and Schema

// Define the blueprint (Schema) for a User document
const userSchema = new Schema(
  {
    // Field: Definition Object
    name: { type: String, required: true }, // name must be a String and is required
    title: { type: String, required: false, default: 'Öğrenci' }, // title is String, not required, defaults to 'Öğrenci'
    email: { type: String, required: true, unique: true }, // email is String, required, and must be unique
    password: { type: String, required: false }, // password is String, not required (for Atlas users)
    isAdmin: { type: Boolean, default: false }, // isAdmin is true/false, defaults to false
    // Linking to other data: An array of Task IDs this user might be related to
    tasks: [{ type: Schema.Types.ObjectId, ref: "Task" }], 
    isActive: { type: Boolean, default: true },
    
    // Fields specifically for Atlas integration
    isExternal: { type: Boolean, default: true }, 
    atlasUserId: { type: String, default: null, sparse: true, unique: true }, // Atlas User ID - String, unique, can be null
    guid: { type: String, default: null }, // Atlas User GUID - String, can be null
    username: { type: String, default: null }, // Atlas SAM Account Name - String, can be null
    // ... other fields from Atlas integration ...
  },
  { timestamps: true } // Add createdAt and updatedAt date fields automatically
);

// Create the Model from the Schema
const User = mongoose.model("User", userSchema);

export default User; // Export the Model so controllers can use it
```
*   We import `Schema` from `mongoose`.
*   `new Schema({...}, { timestamps: true })` creates a new schema. The first object `{...}` defines the fields and their properties. The second object `{ timestamps: true }` is an option that automatically adds `createdAt` and `updatedAt` fields to every document, recording when it was first created and last modified – very useful!
*   Inside the main object, each key is a field name (like `name`, `email`, `isAdmin`). The value is another object defining the properties of that field.
    *   `type`: Specifies the data type (e.g., `String`, `Boolean`, `Number`, `Date`). Mongoose supports various types.
    *   `required`: If `true`, a document *must* have a value for this field to be saved.
    *   `unique`: If `true`, the database ensures no two documents have the same value for this field (like email addresses). `sparse: true` on `atlasUserId` means the `unique` constraint only applies to documents where `atlasUserId` is *not* null.
    *   `default`: If a value isn't provided when creating a document, this value is used automatically.
    *   `[{ type: Schema.Types.ObjectId, ref: "Task" }]`: This is how we define relationships. `Schema.Types.ObjectId` means this field (or items in this array) will store the unique ID of another document. `ref: "Task"` tells Mongoose which Model this ID is expected to refer to. This allows Mongoose to potentially "populate" (fetch) the actual linked Task data later if needed.

*   `const User = mongoose.model("User", userSchema);` does the magic! It takes the `userSchema` blueprint and compiles it into a usable `User` Model. The first argument `"User"` is the name of the model (conventionally singular, capitalized). Mongoose will look for a collection named `users` (lowercase, plural) in the database for this model.
*   `export default User;` makes the `User` model available to other files (like our controllers) via `import`.

Now let's look at a simplified `taskModel.js`:

```javascript
// server\models\taskModel.js (Simplified Schema Definition)
import mongoose, { Schema } from "mongoose";

const taskSchema = new Schema(
  {
    title: { type: String, required: true },
    description: { type: String, default: "" },
    date: { type: Date, default: Date.now }, // Default date to now
    priority: { 
      type: String, 
      default: "normal", 
      enum: ["high", "medium", "normal", "low"] // Restrict allowed values
    },
    stage: { 
      type: String, 
      default: "todo", 
      enum: ["todo", "in progress", "completed"] // Restrict allowed values
    },
    progress: { type: Number, default: 0, min: 0, max: 100 }, // Number with min/max validation

    // Relationships:
    team: [{ type: Schema.Types.ObjectId, ref: "User" }], // Array of User IDs (assignees)
    createdBy: { type: Schema.Types.ObjectId, ref: "User" }, // ID of the User who created it
    parent: { type: Schema.Types.ObjectId, ref: "Task", default: null }, // ID of a parent Task (for hierarchy)
    children: [{ type: Schema.Types.ObjectId, ref: "Task" }], // Array of child Task IDs
    
    assets: [String], // Array of Strings (for file URLs)
    isTrashed: { type: Boolean, default: false },
    // ... other fields like activities, mandays, cost ...
  },
  { timestamps: true }
);

const Task = mongoose.model("Task", taskSchema);

export default Task;
```
*   This schema defines the fields for a task.
*   We see more data types (`Number`) and validation options (`enum` for restricting strings to a list, `min`/`max` for numbers).
*   Crucially, it shows multiple relationships using `Schema.Types.ObjectId` and `ref` to both the `User` model (`team`, `createdBy`) and the `Task` model itself (`parent`, `children`) to build the task hierarchy.

Finally, a look at the simplified `checklistModel.js`:

```javascript
// server\models\checklistModel.js (Simplified Schema Definition)
import mongoose, { Schema } from "mongoose";

// Define a Schema for items within a checklist (a sub-schema)
const checklistItemSchema = new Schema(
  {
    title: { type: String, required: true },
    completed: { type: Boolean, default: false },
    // ... other item details ...
    createdBy: { type: Schema.Types.ObjectId, ref: "User", required: true }, // Item also linked to a user
  },
  { timestamps: true }
);

// Define the main Schema for a Checklist
const checklistSchema = new Schema(
  {
    title: { type: String, required: true },
    description: { type: String },
    user: { type: Schema.Types.ObjectId, ref: "User", required: true }, // Link the whole checklist to a User
    items: [checklistItemSchema], // Array of documents following the checklistItemSchema
    isTrashed: { type: Boolean, default: false },
    // ... other fields ...
  },
  { timestamps: true }
);

const Checklist = mongoose.model("Checklist", checklistSchema);

export default Checklist;
```
*   This file shows how you can even define schemas for nested documents (like `checklistItemSchema` for the items within a checklist). The `items` field in the main `checklistSchema` is an array of documents that follow the `checklistItemSchema`.
*   It again demonstrates linking to the `User` model.

These model files are the single source of truth for the structure of our data in the database. Any time the backend needs to save or fetch user, task, or checklist data, it uses the corresponding Model, which enforces the rules defined in its Schema.

### How Models, Controllers, and the Database Work Together

Let's visualize the interaction when a controller needs to save or retrieve data:

```mermaid
sequenceDiagram
    participant Controller as Backend Controller
    participant Model as Mongoose Model<br>(e.g., User, Task)
    participant Driver as Mongoose/MongoDB Driver
    participant Database as MongoDB Database

    Controller->>Model: Call a method (e.g., Task.create({...}))
    Model->>Model: Validate data against Schema
    Model->>Driver: Prepare command for database (e.g., INSERT document)
    Driver->>Database: Send command
    Database->>Database: Store/Retrieve data
    Database-->>Driver: Result (success/error, data)
    Driver-->>Model: Pass result back
    Model-->>Controller: Return result
    Controller->>Controller: Use result to build response
```

1.  The **Backend Controller** needs to perform a database operation (like creating a task). It calls a method on the corresponding **Mongoose Model** (e.g., `Task.create()`).
2.  The **Model**, using the rules defined in its **Schema**, first checks if the data is valid (e.g., is the `title` a string and present? Is `progress` between 0 and 100?).
3.  The **Model** then uses the underlying **Mongoose/MongoDB Driver** to translate the operation into a command that MongoDB understands.
4.  The **Driver** sends this command over the network to the **MongoDB Database**.
5.  The **Database** executes the command (saves the document, fetches data, etc.).
6.  The **Database** sends the result back to the **Driver**.
7.  The **Driver** passes the result back to the **Model**.
8.  The **Model** returns the result (the created document, the fetched data, or an error) back to the **Controller**.
9.  The **Controller** uses this result to finish processing the request and build the response sent back to the frontend ([API Endpoints and Controllers (Backend)](05_api_endpoints_and_controllers__backend__.md)).

This flow ensures that our data interactions are consistent, validated, and easier to manage because the structure is clearly defined by the Models.

### Conclusion

In this chapter, we explored **Database Models**, the essential blueprints that define the structure of our data in the MongoDB database using Mongoose. We learned that **Schemas** define the fields, types, and rules, and **Models** provide the methods our backend controllers use to interact with the database. We saw how models like `User`, `Task`, and `Checklist` define the shape of the data we store and how controllers use these models to perform operations like finding, creating, and updating documents, which is fundamental to features like user authentication and task management.

Understanding these models is crucial because they dictate how our application's data is organized and accessed on the backend, and consequently, what data is available to be sent to the frontend ([API Communication (RTK Query)](04_api_communication__rtk_query__.md)).

Now that we have a solid understanding of how data is structured and stored, we can look at how the application handles different languages and regions to make it accessible to a wider range of users.

In the next chapter, we will dive into **Internationalization (i18n)**.

[Internationalization (i18n)](08_internationalization__i18n__.md)

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)