# Sade Log Dosyası Örneği

Bu dosya `simple.log` dosyasının nasıl göründüğünü ve nasıl okunacağını gösterir.

## Örnek Log İçeriği

```
2024-01-15 09:00:00 | 🚀 SISTEM BAŞLATILDI | 0.0.0.0:8000 | Mod: PRODUCTION
2024-01-15 09:00:01 | 🤖 BOT YÜKLENDİ | AcademicBot | 3 araç
2024-01-15 09:00:01 | 🤖 BOT YÜKLENDİ | StudentBot | 2 araç
2024-01-15 09:00:02 | 🤖 BOT YÜKLENDİ | AtlasIQBot | 1 araç
2024-01-15 09:05:30 | 🌐 API İSTEK | POST /bots/AcademicBot/query | ✅ 200 | 2.34s
2024-01-15 09:05:30 | ❓ SORGU ALINDI | Bot: AcademicBot | 'Yapay zeka nedir?' | Kullanıcı: user123
2024-01-15 09:05:31 | 🔧 ARAÇLAR SEÇİLDİ | Bot: AcademicBot | SQLQueryTool, DocumentSearchTool
2024-01-15 09:05:32 | ⚙️ ARAÇ ÇALIŞTIRILDI | SQLQueryTool | ✅ BAŞARILI | 0.85s | Found 15 results
2024-01-15 09:05:33 | ⚙️ ARAÇ ÇALIŞTIRILDI | DocumentSearchTool | ✅ BAŞARILI | 0.45s | Found 8 results
2024-01-15 09:05:35 | ✨ SORGU TAMAMLANDI | Bot: AcademicBot | ✅ BAŞARILI | 2.12s | Yanıt: 1250 karakter
2024-01-15 09:10:15 | 🌐 API İSTEK | POST /bots/StudentBot/query | ✅ 200 | 1.89s
2024-01-15 09:10:15 | ❓ SORGU ALINDI | Bot: StudentBot | 'Ödev nasıl yapılır?'
2024-01-15 09:10:16 | 🔧 ARAÇLAR SEÇİLDİ | Bot: StudentBot | WebSearchTool
2024-01-15 09:10:18 | ⚙️ ARAÇ ÇALIŞTIRILDI | WebSearchTool | ✅ BAŞARILI | 1.65s | Found 5 results
2024-01-15 09:10:19 | ✨ SORGU TAMAMLANDI | Bot: StudentBot | ✅ BAŞARILI | 1.78s | Yanıt: 890 karakter
2024-01-15 09:15:45 | 🌐 API İSTEK | POST /bots/UnknownBot/query | ❌ 404 | 0.05s
2024-01-15 09:15:45 | 🚨 HATA | Bot bulunamadı | Bot: UnknownBot
2024-01-15 09:20:30 | 🌐 API İSTEK | POST /bots/AcademicBot/query | ❌ 500 | 5.23s
2024-01-15 09:20:30 | ❓ SORGU ALINDI | Bot: AcademicBot | 'Karmaşık bir sorgu...'
2024-01-15 09:20:31 | 🔧 ARAÇLAR SEÇİLDİ | Bot: AcademicBot | SQLQueryTool
2024-01-15 09:20:35 | ⚙️ ARAÇ ÇALIŞTIRILDI | SQLQueryTool | ❌ BAŞARISIZ | 4.12s
2024-01-15 09:20:35 | 🚨 HATA | Sorgu işleme hatası | Connection timeout | Bot: AcademicBot
2024-01-15 09:25:10 | 🌐 API İSTEK | POST /bots/AcademicBot/clear-memory | ✅ 200 | 0.12s
2024-01-15 09:25:10 | 🧹 HAFIZA TEMİZLENDİ | Bot: AcademicBot | Oturum: session_abc123
2024-01-15 09:30:00 | ⚠️ YAVAŞ İŞLEM | Query processing for AcademicBot | 12.45s (Eşik: 10.00s)
2024-01-15 10:00:00 | 🌐 API İSTEK | POST /reload | ✅ 200 | 1.23s
2024-01-15 10:00:01 | 🔄 KONFİGÜRASYON YENİLENDİ | 3 bot yüklendi
2024-01-15 23:59:59 | 📊 GÜNLÜK ÖZET | Toplam Sorgu: 245 | Başarılı: 230 (%93.9) | Hata: 15
```

## Log Formatı Açıklaması

### Zaman Damgası
```
2024-01-15 09:00:00 |
```
- Tarih ve saat bilgisi
- Kolay okunabilir format

### Emoji'ler ve Anlamları

| Emoji | Anlam | Açıklama |
|-------|-------|----------|
| 🚀 | Sistem Başlatıldı | Uygulama başlatıldığında |
| 🤖 | Bot Yüklendi | Bir bot başarıyla yüklendiğinde |
| 🌐 | API İstek | HTTP API isteği yapıldığında |
| ❓ | Sorgu Alındı | Kullanıcıdan sorgu geldiğinde |
| 🔧 | Araçlar Seçildi | Sorgu için araçlar seçildiğinde |
| ⚙️ | Araç Çalıştırıldı | Bir araç çalıştırıldığında |
| ✨ | Sorgu Tamamlandı | Sorgu işleme tamamlandığında |
| 🧹 | Hafıza Temizlendi | Bot hafızası temizlendiğinde |
| 🔄 | Konfigürasyon Yenilendi | Bot konfigürasyonları yenilendiğinde |
| 🚨 | Hata | Bir hata oluştuğunda |
| ⚠️ | Yavaş İşlem | Performans eşiği aşıldığında |
| 📊 | Günlük Özet | Günlük istatistikler |

### Durum İndikatörleri

| İndikatör | Anlam |
|-----------|-------|
| ✅ | Başarılı |
| ❌ | Başarısız |
| ⚠️ | Uyarı |

### HTTP Status Kodları

| Kod | Emoji | Anlam |
|-----|-------|-------|
| 200-299 | ✅ | Başarılı |
| 400-499 | ⚠️ | İstemci Hatası |
| 500-599 | ❌ | Sunucu Hatası |

## Kullanım Senaryoları

### 1. Sistem Durumunu Kontrol Etme
```bash
# Son 10 satırı göster
tail -10 logs/simple.log

# Canlı takip
tail -f logs/simple.log
```

### 2. Hataları Bulma
```bash
# Tüm hataları göster
grep "🚨 HATA" logs/simple.log

# Son 1 saatteki hataları göster
grep "🚨 HATA" logs/simple.log | grep "$(date '+%Y-%m-%d %H')"
```

### 3. Bot Performansını İzleme
```bash
# Belirli bir bot'un sorgularını göster
grep "Bot: AcademicBot" logs/simple.log

# Yavaş işlemleri göster
grep "⚠️ YAVAŞ İŞLEM" logs/simple.log
```

### 4. API İsteklerini İzleme
```bash
# Tüm API isteklerini göster
grep "🌐 API İSTEK" logs/simple.log

# Başarısız API isteklerini göster
grep "🌐 API İSTEK.*❌" logs/simple.log
```

### 5. Günlük İstatistikleri
```bash
# Günlük özetleri göster
grep "📊 GÜNLÜK ÖZET" logs/simple.log
```

## Avantajları

1. **Kolay Okunabilirlik**: Emoji'ler sayesinde hızlıca durum anlaşılır
2. **Türkçe Açıklamalar**: Teknik olmayan kişiler de anlayabilir
3. **Tek Satır Format**: Her olay tek satırda özetlenir
4. **Hızlı Filtreleme**: Grep ile kolayca filtrelenebilir
5. **Görsel Ayrım**: Farklı olay türleri kolayca ayırt edilir

## Diğer Log Dosyalarıyla Karşılaştırma

| Dosya | Amaç | Hedef Kitle |
|-------|-------|-------------|
| `simple.log` | Hızlı durum kontrolü | Herkes |
| `app.log` | Detaylı sistem logları | Geliştiriciler |
| `api.json` | API analizi | DevOps/Analiz |
| `errors.log` | Hata detayları | Geliştiriciler |
| `queries.json` | Sorgu analizi | Veri analisti |

Bu sade log dosyası, sistemin genel durumunu hızlıca anlamak ve sorunları tespit etmek için idealdir.
