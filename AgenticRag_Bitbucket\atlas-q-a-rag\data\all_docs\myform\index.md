# Tutorial: myform

This project, "myform", is a **web application** for building and managing forms and surveys.
It allows users to **create forms instantly using AI**, collect *responses*, and view *analytics*
through a secure platform requiring **user authentication** via Atlas University credentials.
All data is persistently stored in a *MongoDB database*.


**Source Repository:** [None](None)

```mermaid
flowchart TD
    A0["User Authentication & Management
"]
    A1["Form Structure & Management
"]
    A2["Form Responses & Analytics
"]
    A3["AI Integration
"]
    A4["Database Connection & Models
"]
    A5["Next.js API Routes
"]
    A6["Global Middleware
"]
    A7["Localization (i18n)
"]
    A5 -- "Handles Auth Endpoints" --> A0
    A0 -- "Manages User Data" --> A4
    A6 -- "Enforces Authentication" --> A0
    A5 -- "Manages Forms" --> A1
    A1 -- "Stores Form Structure" --> A4
    A5 -- "Handles Submissions & Analy..." --> A2
    A2 -- "Stores Responses" --> A4
    A5 -- "Accesses AI Services" --> A3
    A3 -- "Generates Forms" --> A1
    A3 -- "Uses Localized Prompts" --> A7
    A6 -- "Filters Requests" --> A5
```

## Chapters

1. [User Authentication & Management
](01_user_authentication___management_.md)
2. [Form Structure & Management
](02_form_structure___management_.md)
3. [Form Responses & Analytics
](03_form_responses___analytics_.md)
4. [AI Integration
](04_ai_integration_.md)
5. [Next.js API Routes
](05_next_js_api_routes_.md)
6. [Database Connection & Models
](06_database_connection___models_.md)
7. [Global Middleware
](07_global_middleware_.md)
8. [Localization (i18n)
](08_localization__i18n__.md)


---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)