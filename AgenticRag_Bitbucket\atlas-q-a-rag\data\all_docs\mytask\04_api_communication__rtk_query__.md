# Chapter 4: API Communication (RTK Query)

Welcome back to the `mytask` tutorial! In [Chapter 1: User and Authentication System](01_user_and_authentication_system_.md), we learned how users get into the system. In [Chapter 2: Task Management Core](02_task_management_core_.md), we saw how tasks are the central focus. And in [Chapter 3: Frontend State Management (Redux Toolkit)](03_frontend_state_management__redux_toolkit__.md), we explored how our frontend application uses Redux Toolkit to manage important data like the logged-in user and lists of tasks in a central, organized way.

But where does all that task data *come from*? And how does the frontend tell the backend to *create* a new task or *update* an existing one?

This is where **API Communication** comes in. It's how the frontend and backend "talk" to each other over the internet. The frontend sends requests (like "Give me the list of tasks") and the backend sends back responses (like "Here are your tasks!").

Doing this manually for every little piece of data can get complicated. You have to manage:
*   Sending the request (using `fetch` or libraries like Axios).
*   Handling loading states (showing a spinner).
*   Handling errors (showing an error message).
*   Storing the received data.
*   Keeping track of whether data is fresh or needs to be re-fetched.
*   Attaching authentication tokens to requests.

That's a lot of boilerplate code! Thankfully, **RTK Query** (which is built on top of Redux Toolkit) is specifically designed to make this process much, much simpler. It automates most of the common tasks involved in fetching and caching data from APIs.

### What is RTK Query?

Imagine your frontend application wants to order something from a restaurant (the backend API).

*   Without RTK Query, you're like a chef who has to not only cook the meal (build the UI) but also call the restaurant, place the order, wait by the door, handle if the order is wrong, and then remember if you've already ordered that dish recently. Tedious!
*   With RTK Query, you have a **specialized order-taker service**. You just tell this service *what* you want (e.g., "the task list"), and it handles *everything else*: calling the restaurant, waiting for the food, giving you the meal when it arrives, even keeping a note of what you ordered recently so it doesn't call unnecessarily if you ask for the same thing twice!

RTK Query helps us define all the possible "orders" (API endpoints) the frontend can place and provides easy-to-use tools (React hooks) to place them and get the result.

### Our Goal: Fetching and Creating Tasks

Let's look at our task examples from [Chapter 2: Task Management Core](02_task_management_core_.md):

1.  **Viewing Tasks:** When you go to the tasks page, the frontend needs to *fetch* the list of tasks from the backend.
2.  **Creating Tasks:** When you fill out the "Create Task" form and click submit, the frontend needs to *send* the new task data to the backend to be saved.

RTK Query makes both of these operations straightforward using generated React hooks.

### Key Concepts of RTK Query (Simplified)

RTK Query uses a few core concepts built on Redux Toolkit:

*   **API Slice:** This is the central place where you define *all* the ways your frontend interacts with a specific backend API. You set the base URL and list all the available "orders" (endpoints). Think of it as the **restaurant's menu** managed by our order-taker service. In code, it's created using `createApi`.

*   **Base Query:** This defines the common logic for *all* requests made through this API slice. It handles things like adding standard headers (like the authentication token) or processing the response. It's the **standard procedure** our order-taker service uses for every call.

*   **Endpoints:** These are the specific items on the menu. Each endpoint defines *one* type of interaction with the backend.
    *   **Queries (`builder.query`):** Used for fetching data (`GET` requests). You ask a question.
    *   **Mutations (`builder.mutation`):** Used for sending data to create, update, or delete something (`POST`, `PUT`, `DELETE`, etc.). You give a command or send new information.

*   **Generated Hooks:** Based on the endpoints you define, RTK Query automatically creates custom React hooks (like `useGetTasksQuery`, `useCreateTaskMutation`). These hooks are the **easy-to-press buttons** you use in your React components to perform the API interaction.

*   **Caching and Invalidation:** RTK Query automatically stores (`caches`) the results of queries. If you request the same data again, it might return the cached result instantly instead of making another network call. When you perform a mutation (like creating a task), you can tell RTK Query which cached queries are now potentially outdated (`invalidatesTags`), and it will automatically re-fetch them (`invalidatesTags` tells it to `invalidate` the `providesTags` cache) to keep your UI up-to-date without manual effort. This is like the order-taker remembering past orders and knowing when a new action means the old information is no longer correct.

*   **Loading and Error States:** The generated hooks automatically provide properties like `isLoading` (true while waiting for the response) and `error` (if something went wrong).

### Using RTK Query to Fetch Tasks (`useGetAllTaskQuery`)

Let's see how the `Tasks.jsx` component uses RTK Query to get the list of tasks, as hinted at in [Chapter 2: Task Management Core](02_task_management_core_.md).

```jsx
// client\src\pages\Tasks.jsx (Simplified)
import { useState } from "react";
// ... other imports like Button, Loading, Tabs ...
// *** Import the generated RTK Query hook for fetching tasks ***
import { useGetAllTaskQuery } from "../redux/slices/api/taskApiSlice";

const Tasks = () => {
  // ... other state like selected view, modal open ...

  // *** Use the hook to fetch tasks and manage state ***
  // This hook automatically handles sending the GET request,
  // storing the result, and managing loading/error states.
  const { data, isLoading, error, refetch } = useGetAllTaskQuery({}); // Pass query parameters if needed

  // RTK Query puts the fetched data in the 'data' property
  // We access the task list from the structure returned by the backend
  const tasks = data?.tasks || [];

  // RTK Query provides isLoading and error states automatically
  if (isLoading) {
    return <div className='py-10'><Loading /></div>; // Show loading spinner
  }
  if (error) {
    return <p>Error loading tasks: {error.message}</p>; // Show error message
  }

  return (
    <div>
      {/* Header and Create Task button */}
      {/* Tabs for different views */}
      {/* Pass the fetched tasks to a view component */}
      {/* ... rendering logic using 'tasks' data ... */}
      {selected === 0 && (
          <div className="p-6">
            {/* Pass fetched tasks to BoardView component */}
            <BoardView tasks={tasks} onTaskUpdate={refetch} />
          </div>
        )}
    </div>
  );
};

export default Tasks;
```
*   We import the specific hook we need: `useGetAllTaskQuery`.
*   We call the hook: `const { data, isLoading, error, refetch } = useGetAllTaskQuery({});`. This one line replaces all the manual logic for starting a fetch, setting loading state, handling success, handling error, and storing the data in Redux!
*   While `isLoading` is true, we show a loading indicator.
*   If there's an `error`, we show an error message.
*   Once the request finishes successfully, `isLoading` becomes false, `error` is null, and the fetched data is available in `data`. We can then access the task list (which the backend provides as `data.tasks`) and use it to render our UI.
*   `refetch` is a function provided by the hook that you can call if you need to manually trigger the data to be re-fetched, although `invalidatesTags` often handles this automatically after mutations.

That's it! Using the hook is incredibly simple. The complexity is defining the API slice and its endpoints.

### Using RTK Query to Create Tasks (`useCreateTaskMutation`)

Now let's look at how the `AddTask.jsx` component uses RTK Query to send data to the backend to create a new task. This uses a mutation hook.

```jsx
// client\src\components\tasks\AddTask.jsx (Simplified)
import { useForm } from "react-hook-form";
import Button from "../Button";
// ... other imports ...
// *** Import the generated RTK Query hook for creating tasks ***
import { useCreateTaskMutation } from "../../redux/slices/api/taskApiSlice";

// AddTask component receives props like 'open', 'setOpen', 'onSuccess'
const AddTask = ({ open, setOpen, onSuccess }) => {
  const { register, handleSubmit } = useForm();

  // *** Use the mutation hook ***
  // This hook returns a function to trigger the mutation (createTask)
  // and state about the mutation's status (isLoading)
  const [createTask, { isLoading }] = useCreateTaskMutation();

  // This function runs when the user submits the form
  const handleOnSubmit = async (data) => {
    try {
      // *** Call the mutation function with the form data ***
      await createTask(data).unwrap(); // .unwrap() throws error if mutation fails

      console.log('Task created successfully!');

      // If successful, close the modal and maybe refetch the task list
      onSuccess(); // onSuccess could be the 'refetch' function from useGetAllTaskQuery
      setOpen(false);

    } catch (err) {
      console.error("Failed to create task:", err);
      // Show an error message to the user (e.g., using a toast/notification library)
    }
  };

  return (
    // Modal wrapper or form layout goes here
    <form onSubmit={handleSubmit(handleOnSubmit)}>
      {/* Input fields for task details */}
      <Textbox label="Task Title" {...register("title")} />
      <textarea placeholder="Description" {...register("description")} />
      {/* ... other form fields ... */}

      {/* Submit button, shows loading state */}
      <Button label="Create Task" type="submit" isLoading={isLoading} />
    </form>
  );
};

export default AddTask;
```
*   We import the `useCreateTaskMutation` hook.
*   When we call the mutation hook, it returns a tuple `[triggerFunction, stateObject]`. We name the trigger function `createTask` and get the state object.
*   Inside `handleOnSubmit`, when the form is submitted, we call `await createTask(data)`. This triggers the `POST` request defined in the RTK Query endpoint. We pass the form `data` as the body of the request.
*   We use `isLoading` from the hook to disable the button or show a spinner while the request is in progress.
*   The `onSuccess()` call after a successful creation is often used to trigger a re-fetch of the task list using the `refetch` function provided by the `useGetAllTaskQuery` hook in the parent `Tasks` component. This is where `invalidatesTags` is powerful – if configured correctly, the `createTask` mutation's `invalidatesTags` would automatically trigger the `useGetAllTaskQuery`'s re-fetch without needing `onSuccess`! (The provided code does use `invalidatesTags`).

### Behind the Scenes: The RTK Query Flow

Let's trace what happens when you use one of these hooks, like `useGetAllTaskQuery`:

```mermaid
sequenceDiagram
    participant Component as React Component
    participant Hook as useGetAllTaskQuery Hook
    participant Middleware as RTK Query Middleware
    participant BaseQuery as fetchBaseQuery
    participant BackendAPI as Backend API

    Component->>Hook: Call useGetAllTaskQuery()
    Hook->>Middleware: Initiate query request
    Middleware->>Middleware: Check cache for existing data
    alt Data is NOT in cache OR cache is invalid
        Middleware->>BaseQuery: Prepare HTTP request<br>(Add base URL, auth headers, etc.)
        BaseQuery->>BackendAPI: Send GET request
        BackendAPI-->>BaseQuery: HTTP Response (data + headers)
        BaseQuery-->>Middleware: Process response
        Middleware->>Middleware: Cache the received data
        Middleware->>Middleware: Update loading state (to false) and error state (to null) in Redux Store
    else Data IS in cache AND is valid
        Middleware->>Middleware: Immediately return cached data
    end
    Middleware-->>Hook: Provide data, isLoading, error from Store
    Hook-->>Component: Return data, isLoading, error
    Component->>Component: Render UI based on data/loading/error
```
1.  A **React Component** calls an RTK Query **Hook** (e.g., `useGetAllTaskQuery()`).
2.  The Hook tells the **RTK Query Middleware** (part of the Redux store setup) that this specific request is needed.
3.  The Middleware first checks if this exact query result is already stored in its **cache** within the Redux Store.
4.  *If* the data isn't cached or the cache is considered outdated (e.g., a relevant mutation occurred), the Middleware uses the **Base Query** to prepare the actual HTTP request. The Base Query adds things like the full URL and authentication headers (like the JWT token stored in Redux or Local Storage, as seen in `apiSlice.js`).
5.  The Base Query sends the HTTP request to the **Backend API**.
6.  The Backend API processes the request and sends back an HTTP **Response** with the data (e.g., the list of tasks) or an error.
7.  The Response goes back through the Base Query and into the Middleware.
8.  The Middleware processes the response, stores the data in its internal **cache** in the Redux Store, and updates the loading and error states for this specific query within the Store.
9.  *If* the data *was* in the cache and still valid, the Middleware skips the network request and immediately retrieves the data from the cache and updates the loading state (to false) and error state (to null) in the Store.
10. In either case, the Hook gets the updated `data`, `isLoading`, and `error` from the Redux Store.
11. The Hook returns these values to the **Component**.
12. The Component sees the updated values and **re-renders** its UI accordingly (shows data, spinner, or error).

This sequence shows how RTK Query hooks abstract away the network logic and hook directly into the Redux Store's state and middleware to manage the data lifecycle.

### Code Deep Dive: Defining the API Interactions

Let's look at the files where these API interactions are defined.

First, we often define a base API slice that contains common settings for all endpoints talking to our main backend API:

```javascript
// client\src\redux\slices\apiSlice.js (Simplified)
import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import appConfig from "../../config/appConfig.js";

// Define the base URL using config
const BASE_URL = appConfig.server.baseUrl + appConfig.server.apiPath; // e.g., "https://apimytask.atlas.edu.tr/api"

// Create a base query that includes common logic
const baseQuery = fetchBaseQuery({
  baseUrl: BASE_URL, // Use the base URL
  credentials: 'include', // Important for sending/receiving cookies (like our auth token)
  prepareHeaders: (headers, { getState }) => {
    // *** Get token from Redux state or localStorage and add it to headers ***
    const { user } = getState().auth; // Get user data from the auth slice state
    const token = user?.token; // Get the token from the user object

    if (token) {
      headers.set('Authorization', `Bearer ${token}`); // Add token as Bearer header
    }

    // Also add the current user ID header for backend filtering
    const userInfo = localStorage.getItem('userInfo');
    if (userInfo) {
        const storedUser = JSON.parse(userInfo);
        if (storedUser?._id) {
            headers.set('x-current-user-id', storedUser._id);
        }
    }

    return headers; // Return the updated headers
  },
});

// Create the base API slice - endpoints will be injected later
export const apiSlice = createApi({
  reducerPath: "api", // Key in the Redux store state where this API's data is kept
  baseQuery, // Use our custom base query
  // tagTypes are used for cache invalidation - list potential data types
  tagTypes: ['Tasks', 'Task', 'User', 'Statistics', 'Groups'],
  endpoints: () => ({}), // Define common endpoints or leave empty to inject later
});
```
*   `createApi` is the core function from RTK Query to define an API slice.
*   `reducerPath: "api"` means this slice's state will be available at `state.api` in the Redux store.
*   `baseQuery` is configured with our base URL and includes `credentials: 'include'` so cookies (like the authentication token cookie set by the backend in [Chapter 1](01_user_and_authentication_system_.md)) are sent.
*   The `prepareHeaders` function runs before every request made through this slice. Here, we access the Redux state (`getState().auth`) to get the logged-in user's token and add it as an `Authorization: Bearer ...` header. We also add a custom `x-current-user-id` header, which the backend uses to know which user is making the request for filtering purposes (like only showing *their* tasks).
*   `tagTypes` is an array of strings representing categories of data (like 'Tasks'). This is crucial for cache management.
*   `endpoints: () => ({})` means we'll define the specific endpoints (like fetching tasks) in separate files using `injectEndpoints`.

Next, we define the task-specific endpoints by injecting them into the base `apiSlice`:

```javascript
// client\src\redux\slices\api\taskApiSlice.js (Simplified)
import { TASKS_URL } from "../../../utils/contants"; // Base path for tasks, e.g., "/tasks"
import { apiSlice } from "../apiSlice"; // Import our base API slice

// Inject endpoints into the base API slice for tasks
export const taskApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Define a query endpoint to get all tasks
    getAllTask: builder.query({
      query: ({ strQuery, isTrashed, search }) => { // Parameters for filtering
        // Build the specific URL for this query, adding parameters
        let url = `${TASKS_URL}?stage=${strQuery}&isTrashed=${isTrashed}&search=${search}`;
        console.log('Fetching tasks with URL:', url);
        return { url, method: "GET" }; // Return the URL and HTTP method
      },
      // Tag this query's cached data with 'Tasks'
      // This means if anything invalidates 'Tasks', this query will re-fetch
      providesTags: ['Tasks'],
    }),

    // Define a mutation endpoint to create a task
    createTask: builder.mutation({
      query: (data) => ({ // The data parameter is the request body
        url: `${TASKS_URL}/create`, // Specific path for creation
        method: "POST", // Use POST for creation
        body: data, // The task data to send
      }),
      // Invalidate the 'Tasks' cache tag after a successful creation
      // This tells RTK Query that the list of tasks might have changed,
      // and any query providing 'Tasks' (like getAllTask) should re-fetch.
      invalidatesTags: ['Tasks'],
    }),

    // Define a query endpoint to get a single task
    getSingleTask: builder.query({
      query: ({ id }) => ({ // id parameter is the task ID
        url: `${TASKS_URL}/${id}`, // URL includes the task ID
        method: "GET",
      }),
      // Tag this query with 'Task' for this specific ID
      // Also invalidates ['Tasks'] because fetching a single task might be part of a list
      providesTags: (result, error, { id }) => [{ type: 'Task', id }, 'Tasks'],
    }),

    // ... other endpoints like updateTask, deleteTask, changeTaskStage ...

  }),
});

// RTK Query automatically generates hooks from the endpoint names
// Export these hooks to be used in components
export const {
  useGetAllTaskQuery, // Hook for the getAllTask query
  useCreateTaskMutation, // Hook for the createTask mutation
  useGetSingleTaskQuery, // Hook for the getSingleTask query
  // ... export other generated hooks ...
} = taskApiSlice;
```
*   `apiSlice.injectEndpoints` is used to add endpoints to an existing API slice (our `apiSlice`).
*   The `endpoints` function receives a `builder` object which has methods like `builder.query` and `builder.mutation`.
*   `builder.query({ query: (...) => ({ ... }), providesTags: [...] })` defines a query. The `query` function prepares the URL and method. `providesTags` associates this query's result with cache tags.
*   `builder.mutation({ query: (...) => ({ ... }), invalidatesTags: [...] })` defines a mutation. The `query` function prepares the request details (URL, method, body). `invalidatesTags` lists the cache tags that should be invalidated after this mutation is successful.
*   Finally, we export the automatically generated hooks (`useGetAllTaskQuery`, `useCreateTaskMutation`, etc.) so our components can easily use them.

We also have a similar API slice for the specific interactions with the Atlas Authentication server, defined in `atlasAuthApiSlice.js`:

```javascript
// client\src\redux\slices\api\atlasAuthApiSlice.js (Simplified)
import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import appConfig from "../../../config/appConfig.js";

// Define the base URL for the Atlas API
const ATLAS_API_BASE_URL = appConfig.atlasApi.baseUrl; // e.g., "https://atlas-auth.example.com"

// Create a dedicated slice for Atlas API interactions
export const atlasApiSlice = createApi({
  reducerPath: 'atlasApi', // State will be at state.atlasApi
  baseQuery: fetchBaseQuery({
    baseUrl: ATLAS_API_BASE_URL,
    prepareHeaders: (headers) => {
      // Atlas API might need different headers than our main API
      headers.set('Content-Type', 'application/json');
      return headers;
    }
  }),
  endpoints: (builder) => ({
    // Define a mutation endpoint for Atlas Login
    atlasLogin: builder.mutation({
      query: (data) => ({ // Login credentials
        url: '/Login/Login', // Specific Atlas login path
        method: 'POST',
        body: data, // Send username/password etc.
      }),
      // transformResponse can modify the response data before it's cached/returned
      transformResponse: (response) => {
        // Map Atlas's 'webToken' to our app's 'token' for consistency
        if (response.webToken) {
          response.token = response.webToken;
        }
        return response;
      },
    }),

    // Define a query endpoint to get Atlas user data using the token
    atlasUserData: builder.query({
      query: (token) => ({ // Takes the token as a parameter
        url: '/Login/LoginData', // Specific Atlas user data path
        method: 'GET',
        headers: {
          Authorization: `Bearer ${token}` // Atlas API requires token in header
        },
      }),
    }),
    // useLazyAtlasUserDataQuery is also generated, useful for fetching on demand
  }),
});

// Export the generated hooks
export const {
  useAtlasLoginMutation, // Hook to perform Atlas login
  useAtlasUserDataQuery, // Hook to fetch Atlas user data
  useLazyAtlasUserDataQuery // Lazy hook to fetch Atlas user data when needed
} = atlasApiSlice;
```
*   This `atlasApiSlice` works the same way but has a different `baseUrl` and potentially different `prepareHeaders` logic specific to the Atlas authentication service.
*   We define the `atlasLogin` mutation and `atlasUserData` query endpoints here.
*   Notice the `transformResponse` on `atlasLogin` – this is a useful feature of RTK Query to adjust the shape of the data received from the API before it's stored or used.
*   We export the corresponding hooks. These are used in the login flow discussed in [Chapter 1: User and Authentication System](01_user_and_authentication_system_.md).

Finally, these API slices are added to the central Redux store, as seen in [Chapter 3: Frontend State Management (Redux Toolkit)](03_frontend_state_management__redux_toolkit__.md):

```javascript
// client\src\redux\store.js (Simplified)
import { configureStore } from "@reduxjs/toolkit";
import authReducer from "./slices/authSlice"; // Our auth slice reducer
import { apiSlice } from "./slices/apiSlice"; // Our main RTK Query API slice
import { atlasApiSlice } from "./slices/api/atlasAuthApiSlice"; // Atlas RTK Query API slice
// ... other imports ...

const store = configureStore({
  reducer: {
    auth: authReducer,
    // Add the reducers generated by our API slices
    [apiSlice.reducerPath]: apiSlice.reducer, // State for main API calls (tasks, users, etc.)
    [atlasApiSlice.reducerPath]: atlasApiSlice.reducer, // State for Atlas API calls
    // ... other reducers ...
  },
  // Add the middleware generated by our API slices
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      // ... serializableCheck config ...
    })
      .concat(apiSlice.middleware) // Add middleware for the main API slice
      .concat(atlasApiSlice.middleware), // Add middleware for the Atlas API slice

  devTools: process.env.NODE_ENV !== 'production',
});

export default store;
```
*   The `reducer` field includes the reducers from both `apiSlice` and `atlasApiSlice`. RTK Query automatically creates these reducers to manage the cached data and the loading/error states for all the endpoints defined within them.
*   The `middleware` field includes the middleware from both slices. This middleware intercepts the actions dispatched by the RTK Query hooks and handles the actual fetching, caching, invalidation, etc., logic.

By setting up the store this way, our React components can simply call the generated hooks (`useGetAllTaskQuery`, `useCreateTaskMutation`, etc.), and RTK Query handles the communication, state management, and caching automatically using the definitions in the API slices and the central store configuration.

### Conclusion

In this chapter, we explored **API Communication** using **RTK Query**, the powerful tool that simplifies how our frontend talks to the backend. We learned that RTK Query helps us define API interactions using **API Slices** and **Endpoints** (Queries for fetching, Mutations for changing data) and provides easy-to-use, auto-generated **Hooks** to use these interactions in our components. We saw how it automates loading/error states, handles authentication headers using a **Base Query**, and manages fetched data efficiently through **Caching** and **Invalidation**.

Understanding RTK Query is key to seeing how data flows from the backend into our Redux state and onto the user interface, as well as how user actions trigger updates on the server.

So far, we've focused on the *frontend's* side of the conversation – how it asks for or sends data. But what is the backend doing when it receives these requests?

In the next chapter, we'll switch gears and dive into the backend to understand **API Endpoints and Controllers**. We'll see how the backend receives the requests sent by RTK Query, processes them, interacts with the database, and sends back the responses that RTK Query then manages on the frontend.

[API Endpoints and Controllers (Backend)](05_api_endpoints_and_controllers__backend__.md)

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)