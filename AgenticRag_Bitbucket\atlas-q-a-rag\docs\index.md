# Tutorial: atlas-q-a-rag

This project is an **Agentic RAG system** that organizes its capabilities
into specialized *Bots*. Each *Bot* uses a *Query Router* to determine
which external *Tools* (like database queries or document search) are
needed to gather information based on the user's query. The results
from these *Tools* are then processed by an *Agent* which uses
conversation *Memory* and *Bot Configurations* to formulate the final,
informed response. A *Document Processor* component handles preparing
documents for the document search tool.


**Source Repository:** [None](None)

```mermaid
flowchart TD
    A0["AgenticRAG System
"]
    A1["Bot
"]
    A2["Tool
"]
    A3["Query Router
"]
    A4["Agent (LangGraphAgent)
"]
    A5["Bot Configuration
"]
    A6["Memory Management
"]
    A7["Document Processor
"]
    A0 -- "Loads Configuration" --> A5
    A0 -- "Manages Bots" --> A1
    A0 -- "Uses Router" --> A3
    A0 -- "Uses Agent" --> A4
    A3 -- "Executes Tool" --> A2
    A2 -- "Queries Data From" --> A7
    A4 -- "Uses Memory" --> A6
    A4 -- "Processes Tool Results" --> A2
    A3 -- "Reads Configuration" --> A5
    A4 -- "Reads Configuration" --> A5
    A0 -- "Manages Memory" --> A6
```

## Chapters

1. [AgenticRAG System
](01_agenticrag_system_.md)
2. [Bot
](02_bot_.md)
3. [Query Router
](03_query_router_.md)
4. [Tool
](04_tool_.md)
5. [Agent (LangGraphAgent)
](05_agent__langgraphagent__.md)
6. [Memory Management
](06_memory_management_.md)
7. [Bot Configuration
](07_bot_configuration_.md)
8. [Document Processor
](08_document_processor_.md)


---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)