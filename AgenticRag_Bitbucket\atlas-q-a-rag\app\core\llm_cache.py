"""
Simple LLM response caching system for the Agentic RAG system.
Provides in-memory caching with TTL (Time To Live) support.
"""

import hashlib
import logging
import time
from typing import Any, Dict, Optional
from threading import Lock

logger = logging.getLogger(__name__)


class LLMCache:
    """
    Simple in-memory cache for LLM responses with TTL support.
    Thread-safe implementation for concurrent access.
    """

    def __init__(self, max_size: int = 1000, default_ttl: int = 3600):
        """
        Initialize the LLM cache.

        Args:
            max_size: Maximum number of entries to store
            default_ttl: Default time-to-live in seconds (1 hour)
        """
        self.max_size = max_size
        self.default_ttl = default_ttl
        self.cache: Dict[str, Dict[str, Any]] = {}
        self.access_times: Dict[str, float] = {}
        self.lock = Lock()
        
        logger.info(f"Initialized LLM cache with max_size={max_size}, default_ttl={default_ttl}s")

    def _generate_key(self, prompt: str, model: str = "default", **kwargs) -> str:
        """
        Generate a cache key from prompt and parameters.

        Args:
            prompt: The LLM prompt
            model: Model name
            **kwargs: Additional parameters that affect the response

        Returns:
            A hash key for caching
        """
        # Create a string representation of all parameters
        cache_data = {
            "prompt": prompt,
            "model": model,
            **kwargs
        }
        
        # Convert to string and hash
        cache_string = str(sorted(cache_data.items()))
        return hashlib.md5(cache_string.encode()).hexdigest()

    def _is_expired(self, entry: Dict[str, Any]) -> bool:
        """Check if a cache entry is expired."""
        return time.time() > entry["expires_at"]

    def _cleanup_expired(self) -> None:
        """Remove expired entries from cache."""
        current_time = time.time()
        expired_keys = [
            key for key, entry in self.cache.items()
            if current_time > entry["expires_at"]
        ]
        
        for key in expired_keys:
            del self.cache[key]
            del self.access_times[key]
        
        if expired_keys:
            logger.debug(f"Cleaned up {len(expired_keys)} expired cache entries")

    def _evict_lru(self) -> None:
        """Evict least recently used entries if cache is full."""
        if len(self.cache) >= self.max_size:
            # Find the least recently used key
            lru_key = min(self.access_times.keys(), key=lambda k: self.access_times[k])
            del self.cache[lru_key]
            del self.access_times[lru_key]
            logger.debug(f"Evicted LRU cache entry: {lru_key[:8]}...")

    def get(self, prompt: str, model: str = "default", **kwargs) -> Optional[Any]:
        """
        Get a cached response.

        Args:
            prompt: The LLM prompt
            model: Model name
            **kwargs: Additional parameters

        Returns:
            Cached response or None if not found/expired
        """
        key = self._generate_key(prompt, model, **kwargs)
        
        with self.lock:
            # Clean up expired entries periodically
            if len(self.cache) > 0 and time.time() % 100 < 1:  # Cleanup roughly every 100 seconds
                self._cleanup_expired()
            
            if key in self.cache:
                entry = self.cache[key]
                
                # Check if expired
                if self._is_expired(entry):
                    del self.cache[key]
                    del self.access_times[key]
                    logger.debug(f"Cache entry expired: {key[:8]}...")
                    return None
                
                # Update access time
                self.access_times[key] = time.time()
                logger.debug(f"Cache hit: {key[:8]}...")
                return entry["response"]
            
            logger.debug(f"Cache miss: {key[:8]}...")
            return None

    def set(self, prompt: str, response: Any, model: str = "default", ttl: Optional[int] = None, **kwargs) -> None:
        """
        Store a response in cache.

        Args:
            prompt: The LLM prompt
            response: The LLM response to cache
            model: Model name
            ttl: Time-to-live in seconds (uses default if None)
            **kwargs: Additional parameters
        """
        key = self._generate_key(prompt, model, **kwargs)
        ttl = ttl or self.default_ttl
        
        with self.lock:
            # Evict LRU if necessary
            self._evict_lru()
            
            # Store the entry
            self.cache[key] = {
                "response": response,
                "created_at": time.time(),
                "expires_at": time.time() + ttl,
                "ttl": ttl
            }
            self.access_times[key] = time.time()
            
            logger.debug(f"Cached response: {key[:8]}... (TTL: {ttl}s)")

    def clear(self) -> None:
        """Clear all cache entries."""
        with self.lock:
            cleared_count = len(self.cache)
            self.cache.clear()
            self.access_times.clear()
            logger.info(f"Cleared {cleared_count} cache entries")

    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        with self.lock:
            current_time = time.time()
            expired_count = sum(
                1 for entry in self.cache.values()
                if current_time > entry["expires_at"]
            )
            
            return {
                "total_entries": len(self.cache),
                "expired_entries": expired_count,
                "active_entries": len(self.cache) - expired_count,
                "max_size": self.max_size,
                "default_ttl": self.default_ttl
            }


# Global cache instance
_global_cache = None


def get_llm_cache() -> LLMCache:
    """Get the global LLM cache instance."""
    global _global_cache
    if _global_cache is None:
        _global_cache = LLMCache()
    return _global_cache


def clear_llm_cache() -> None:
    """Clear the global LLM cache."""
    global _global_cache
    if _global_cache:
        _global_cache.clear()
