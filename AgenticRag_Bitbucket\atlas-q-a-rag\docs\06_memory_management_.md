# Chapter 6: Memory Management

Welcome back! In our journey through the `atlas-q-a-rag` system, we've explored the central [AgenticRAG System](01_agenticrag_system_.md), the specialized [Bot](02_bot_.md)s, the [Query Router](03_query_router_.md) that decides which tools to use, the [Tool](04_tool_.md)s themselves that fetch external information, and in the last chapter, the [Agent (LangGraphAgent)](05_agent__langgraphagent__.md), which acts as the "brain" synthesizing the final answer.

But there's one crucial piece missing for building truly conversational bots: **Memory**.

## What Problem Does Memory Management Solve?

Imagine talking to a friend. If you ask, "Did you see that movie last night?", they understand "that movie" because you probably just talked about it. Then, if you ask, "What did you think of the main actor?", they know you mean the actor *in that specific movie*. They remember the context of your conversation.

Now, imagine asking a bot:
**User:** "Tell me about the latest research on fusion energy."
**Bot:** "Okay, here is some information about recent advancements in fusion energy..." (Provides details)
**User:** "What about its potential impact?"

If the bot doesn't remember the previous turn, it might not know what "its" refers to! It might ask, "Potential impact of *what*?". This is frustrating for the user.

The problem is: **How can our bots remember the previous questions and answers in a conversation so they can understand follow-up questions and provide contextually relevant responses?**

This is exactly what the **Memory Management** component handles. It's like giving each user's conversation with a specific bot a short-term memory.

It allows the system to:

1.  **Keep track** of the back-and-forth messages for a specific user session.
2.  **Provide this history** to the [Agent (LangGraphAgent)](05_agent__langgraphagent__.md) when it's generating a response.
3.  Enable **multi-turn interactions** where the bot can refer to previous parts of the conversation.

## The Concept: Conversation History

At its core, memory management is about storing a sequence of messages – who said what, and in what order – for a particular conversation session.

Think of it as a logbook for each chat thread:

*   User: "What's the weather like today?"
*   AI: "The weather is sunny."
*   User: "And tomorrow?"

The "And tomorrow?" only makes sense if the AI remembers the user was asking about the *weather* from the previous turn.

To keep track of different conversations happening at the same time (maybe multiple users talking to the same bot, or one user talking to different bots), we need a way to identify *which* conversation a message belongs to. This is done using a **Session ID**.

A **Session ID** is a unique identifier for a single, ongoing conversation between a user and a specific bot. When you send a query to the system, you can include a session ID. If you provide the same session ID for subsequent queries, the system knows those queries are part of the same conversation thread.

## Where Does Memory Management Fit in the Flow?

Memory management is primarily integrated with the [Agent (LangGraphAgent)](05_agent__langgraphagent__.md), as the agent is the component that needs the conversation history to generate a contextually aware response.

Here's the flow we saw in Chapter 5, now highlighting the Memory Management interaction:

```mermaid
sequenceDiagram
    participant User
    participant FastAPIA as FastAPI App
    participant AgenticRAG as AgenticRAG System
    participant Agent as Agent (LangGraphAgent)
    participant Memory as Memory Manager

    User->>FastAPIA: Query Request (with Session ID)
    FastAPIA->>AgenticRAG: process_query(user_query, session_id, ...)
    AgenticRAG->>Agent: process_query(user_query, tool_results, session_id)
    Agent->>Memory: Get Chat History (using session_id)
    Memory-->>Agent: Chat History
    Agent->>Agent: Use tool results, query, history, prompts (via LLM)
    Agent->>Memory: Save Query & Response (using session_id)
    Agent-->>AgenticRAG: Final Response & Metadata
    AgenticRAG-->>FastAPIA: QueryResponse
    FastAPIA->>User: JSON Response
```

As you can see, the `session_id` provided by the user travels through the system to the [Agent (LangGraphAgent)](05_agent__langgraphagent__.md). The Agent then uses this ID to fetch the history *from* the Memory Manager before generating the response, and saves the current turn *to* the Memory Manager after generating the response.

## How to Use Memory (From the Outside)

As a user interacting with the API, you enable memory for a conversation by simply providing a unique `session_id` in your `QueryRequest` when you call the `/bots/{bot_name}/query` endpoint.

The `QueryRequest` model includes an optional `session_id` field:

```python
# Simplified from app\models\api_models.py
from pydantic import BaseModel
from typing import Optional, Dict, Any

class QueryRequest(BaseModel):
    query: str
    session_id: Optional[str] = None # <--- This is where you provide the ID
    metadata: Optional[Dict[str, Any]] = None
```

If you make a request *without* a `session_id`, the conversation will be stateless – the bot will not remember anything from previous turns.

If you provide a `session_id`, the system will look for existing memory associated with that ID. If it finds it, it will use it. If not, it will create a new memory for that ID. Subsequent requests with the *same* `session_id` will add to and retrieve from this same memory.

Here's how you might send a request with a `session_id` using `curl`:

```bash
# Example curl request to start a session and ask a question
# Let's use a simple uuid for the session ID (you'd generate a real one)
curl -X POST "http://localhost:8000/bots/simple_bot/query" \
-H "Content-Type: application/json" \
-d '{
  "query": "What is the capital of France?",
  "session_id": "my-unique-session-123"
}'
```

```bash
# Example curl request for a follow-up question using the SAME session ID
curl -X POST "http://localhost:8000/bots/simple_bot/query" \
-H "Content-Type: application/json" \
-d '{
  "query": "And what is its population?",
  "session_id": "my-unique-session-123" # <--- Reusing the same ID
}'
```

In the second request, because you reused `"my-unique-session-123"`, the bot (specifically its [Agent (LangGraphAgent)](05_agent__langgraphagent__.md)) will have access to the previous turn ("What is the capital of France?" -> "Paris.") and understand that "its" refers to Paris.

## How Memory is Used Internally

The [AgenticRAG System](01_agenticrag_system_.md) is the first component to receive the `session_id` from the API request. It simply passes this ID along when it calls the [Agent (LangGraphAgent)](05_agent__langgraphagent__.md)'s `process_query` method:

```python
# File: app\core\agentic_rag.py (simplified)

    async def process_query(
        self, bot_name: str, request: QueryRequest
    ) -> QueryResponse:
        # ... get bot and router ...
        tool_results = await query_router.route_query(...)

        # Get the agent instance for this bot
        agent: LangGraphAgent = bot["agent"]

        # Call the agent's process_query method, passing the session_id
        agent_response = await agent.process_query(
            request.query,
            tool_results["tool_responses"],
            session_id=request.session_id, # <--- session_id is passed here
        )
        # ... format and return response ...
```

The actual interaction with the Memory Management component happens *inside* the `LangGraphAgent`.

## Under the Hood: The MemoryManager Class

Let's look at the `app/core/memory_manager.py` file. This is where the `MemoryManager` class lives. It uses LangChain's `ConversationBufferMemory` under the hood, which is a simple way to store messages in a buffer.

### Storing Memories per Session

The `MemoryManager` keeps track of different conversations using a dictionary where the keys are the `session_id`s and the values are the `ConversationBufferMemory` objects.

```python
# File: app\core\memory_manager.py (simplified)

from typing import Dict
from langchain.memory import ConversationBufferMemory
# ... other imports ...

class MemoryManager:
    """
    Memory manager...
    """

    def __init__(self):
        """Initialize the memory manager."""
        # Dictionary to store conversation memories by session_id
        self.memories: Dict[str, ConversationBufferMemory] = {} # <--- The core storage
        logger.info("MemoryManager initialized.")

    def get_memory(self, session_id: str) -> ConversationBufferMemory:
        """
        Get or create a memory for a session.
        """
        # Check if memory for this session_id already exists
        if session_id not in self.memories:
            # If not, create a new ConversationBufferMemory instance
            logger.info(f"Creating new memory for session: {session_id}")
            self.memories[session_id] = ConversationBufferMemory(
                return_messages=True, # Get history as message objects
                memory_key="chat_history", # Key used internally by Langchain
                input_key="query",      # Key for user input
                output_key="response",  # Key for AI output
            )
        # Return the existing or newly created memory object
        return self.memories[session_id]

    # ... methods to add messages, get history, clear memory ...
```

**Explanation:**

*   `self.memories`: This dictionary is the heart of the manager. It maps each unique `session_id` (a string) to a specific `ConversationBufferMemory` instance dedicated to that session.
*   `get_memory(session_id)`: This method is called whenever memory is needed for a specific `session_id`. It first checks if an entry for that ID exists in `self.memories`. If not, it creates a new `ConversationBufferMemory` object, configures it, and stores it in the dictionary before returning it. If it exists, it just returns the existing one. This ensures that consecutive calls with the same `session_id` access the same conversation history.

### Adding Messages to Memory

After the [Agent (LangGraphAgent)](05_agent__langgraphagent__.md) processes a turn (receives a user query and generates an AI response), it needs to save this exchange to the memory associated with the session.

```python
# File: app\core\memory_manager.py (simplified)

    def add_user_message(self, session_id: str, message: str) -> None:
        """
        Add a user message to the conversation history.
        """
        # Get the memory object for this session
        memory = self.get_memory(session_id)
        # Use Langchain's method to add the user message
        memory.chat_memory.add_user_message(message)
        logger.debug(f"Added user message to session {session_id}...")

    def add_ai_message(self, session_id: str, message: str) -> None:
        """
        Add an AI message to the conversation history.
        """
        # Get the memory object for this session
        memory = self.get_memory(session_id)
        # Use Langchain's method to add the AI message
        memory.chat_memory.add_ai_message(message)
        logger.debug(f"Added AI message to session {session_id}...")
```

**Explanation:**

*   `add_user_message` and `add_ai_message`: These methods take the `session_id` and the message content. They retrieve the correct `ConversationBufferMemory` object using `self.get_memory(session_id)` and then use the underlying LangChain methods (`add_user_message`, `add_ai_message`) to add the message to the history buffer for that specific session.

### Retrieving Chat History

Before the [Agent (LangGraphAgent)](05_agent__langgraphagent__.md) generates a response, it needs the past conversation history to understand the context. The `MemoryManager` provides a method to get this history.

```python
# File: app\core\memory_manager.py (simplified)

from langchain_core.messages import BaseMessage, HumanMessage, AIMessage
from typing import List # Import List for type hinting

    def get_chat_history(self, session_id: str) -> List[BaseMessage]:
        """
        Get the chat history for a session as a list of message objects.
        """
        memory = self.get_memory(session_id)
        # Use Langchain's method to retrieve the messages
        return memory.chat_memory.messages

    def get_chat_history_str(self, session_id: str) -> str:
        """
        Get the chat history for a session as a formatted string.
        """
        # Get the history as a list of message objects
        messages = self.get_chat_history(session_id)
        if not messages:
            return ""

        # Format the messages into a simple string format
        history_str = ""
        for message in messages:
            if isinstance(message, HumanMessage):
                history_str += f"Human: {message.content}\n"
            elif isinstance(message, AIMessage):
                history_str += f"AI: {message.content}\n"
            # Handle other message types if necessary
            else:
                 history_str += f"{message.type}: {message.content}\n"

        return history_str # Return the formatted string
```

**Explanation:**

*   `get_chat_history`: Returns the history as a list of LangChain `BaseMessage` objects (`HumanMessage` and `AIMessage`). This is useful if the Agent's LLM framework prefers message objects.
*   `get_chat_history_str`: This is often more convenient for prompting an LLM. It iterates through the message objects, formats each one (e.g., "Human: ...", "AI: ..."), and combines them into a single string. This string representation of the history is what's passed to the Agent's prompt.

### Clearing Memory

Sometimes, you might want to start a conversation fresh without any memory of previous turns. The `MemoryManager` allows you to clear the history for a specific session ID.

```python
# File: app\core\memory_manager.py (simplified)

    def clear_memory(self, session_id: str) -> None:
        """
        Clear the memory for a session.
        """
        # Check if memory exists for this session
        if session_id in self.memories:
            logger.info(f"Clearing memory for session: {session_id}")
            # Simply remove the entry from the dictionary
            del self.memories[session_id]
        else:
             logger.info(f"No memory found to clear for session: {session_id}")
```

**Explanation:**

*   `clear_memory(session_id)`: This method removes the `ConversationBufferMemory` object associated with the given `session_id` from the `self.memories` dictionary. The next time a request comes in with the *same* `session_id`, `get_memory` will not find it and will create a brand new, empty memory for that ID, effectively starting a new conversation thread.

The API provides an endpoint (`POST /bots/{bot_name}/clear-memory`) to call this method via the [AgenticRAG System](01_agenticrag_system_.md), allowing external applications to reset a user's conversation history.

## Integrating Memory in the Agent

Now, let's circle back to the `LangGraphAgent` (`app/agents/langgraph_agent.py`) to see how it uses the `MemoryManager`.

Recall that the `LangGraphAgent` initializes its own `MemoryManager` instance:

```python
# File: app\agents\langgraph_agent.py (simplified)

from app.core.memory_manager import MemoryManager # Import the MemoryManager class

class LangGraphAgent:
    def __init__(...):
        # ... initialize LLM ...

        # Initialize memory manager
        self.memory_manager = MemoryManager() # <--- Instance of MemoryManager

        # Initialize the graph
        self.graph = self._build_graph()
```

Then, in the `process_query` method, before running the graph, it retrieves the history, and after the graph finishes, it saves the current turn.

```python
# File: app\agents\langgraph_agent.py (simplified process_query)

    async def process_query(
        self, query: str, tool_results: Dict[str, Any], session_id: Optional[str] = None
    ) -> Dict[str, Any]:
        # ... log tool usage ...

        # 1. Get chat history using the MemoryManager
        chat_history = None
        if session_id: # Only retrieve history if a session_id is provided
            chat_history = self.memory_manager.get_chat_history_str(session_id) # <--- Get history string
            logger.info(f"Retrieved chat history for session {session_id}")

        # 2. Initialize the state for the graph
        initial_state: AgentState = {
            "query": query,
            "context": [],
            "tool_results": tool_results,
            "response": None,
            "error": None,
            "chat_history": chat_history, # <--- Pass history into the state
        }

        # 3. Run the LangGraph graph!
        result = self.graph.invoke(initial_state)

        # 4. Save the user query and agent's response to memory
        if session_id: # Only save if a session_id is provided
            # Add user message
            self.memory_manager.add_user_message(session_id, query) # <--- Save user message
            # Add AI message (if response was generated)
            if result["response"]:
                self.memory_manager.add_ai_message(session_id, result["response"]) # <--- Save AI response
                logger.info(f"Updated conversation memory for session {session_id}")

        # 5. Return the final response
        return {
            "query": query,
            "response": result["response"],
            "error": result["error"],
        }
    # ... _build_graph method ...
```

**Explanation:**

*   At the beginning, if a `session_id` is present, the agent calls `self.memory_manager.get_chat_history_str(session_id)` to fetch the history string.
*   This history string is added to the `initial_state` that is passed into the LangGraph workflow.
*   Inside the `generate_response` node of the LangGraph, the prompt template uses this `chat_history` from the state to include the past conversation in the prompt sent to the LLM.
*   After the graph finishes and returns the final state containing the generated `response`, the agent checks again if `session_id` was provided.
*   If it was, it calls `self.memory_manager.add_user_message(session_id, query)` and `self.memory_manager.add_ai_message(session_id, result["response"])` to append the user's query and the bot's response to the memory buffer for that session.

This simple pattern of **get history -> process -> save history** is fundamental to enabling stateful, multi-turn conversations.

## Conclusion

In this chapter, we explored **Memory Management**, the component responsible for giving our bots the ability to remember past conversation turns within a specific user session. We learned that this is crucial for handling follow-up questions and providing contextually relevant responses in multi-turn interactions. Memory is managed per `session_id` using the `MemoryManager` class, which stores and retrieves conversation history using LangChain's `ConversationBufferMemory`. The [Agent (LangGraphAgent)](05_agent__langgraphagent__.md) integrates with the `MemoryManager` to fetch history before processing a query and save the current turn afterwards. External applications can enable memory by providing a `session_id` in the query request and clear it using the dedicated endpoint.

With our understanding of how bots remember conversations, we have now covered all the core conceptual components *within* the system itself. In the next chapter, we'll shift our focus to **Bot Configuration** – how we define and set up all these components ([Query Router](03_query_router_.md), [Tool](04_tool_.md)s, [Agent (LangGraphAgent)](05_agent__langgraphagent__.md), etc.) for each specific [Bot](02_bot_.md) using configuration files.

[Bot Configuration](07_bot_configuration_.md)

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)