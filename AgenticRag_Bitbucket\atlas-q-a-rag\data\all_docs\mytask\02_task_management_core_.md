# Chapter 2: Task Management Core

Welcome back! In [Chapter 1: User and Authentication System](01_user_and_authentication_system_.md), we learned how users get securely into the `mytask` application. Now that you're inside the building, it's time to explore the main purpose of our app: **managing tasks!**

### What is Task Management Core?

Think of the Task Management Core as the entire system responsible for handling all the tasks within `mytask`. It's the **engine room** where tasks are created, worked on, organized, and completed.

This system lets you:

*   **Create** new tasks (like adding an item to a to-do list).
*   **View** your tasks (seeing everything you need to do).
*   **Update** tasks (changing their status, adding details, etc.).
*   **Delete** tasks (removing finished or unwanted items).
*   **Organize** tasks using things like **priority** (how important it is), **stage** (where it is in the process, like "to-do," "in progress," "done"), and **team members** (who is working on it).
*   Manage **subtasks** and understand **task hierarchies** (breaking big tasks into smaller ones).
*   **Attach files** (adding documents or images related to a task).

It's the central feature that helps you (and your team) keep track of everything that needs to get done.

### Our Goal: Creating a Task

Let's focus on the most fundamental action: **creating a new task**. This is often the first step in getting organized.

In the `mytask` app, you'll have a way to add a new task, providing details like what the task is, who should do it, and when it's due.

Let's see how this works from the user's perspective and then peek behind the scenes.

### Creating a Task (The User Experience)

When you want to add a new task, you'll typically click a button (like "+ Create Task") which opens a form. This form is where you input all the details.

Here's a simplified look at the form you might see (using parts of the `AddTask.jsx` component):

```jsx
// client\src\components\tasks\AddTask.jsx (Simplified)
import { useForm } from "react-hook-form";
import Button from "../Button";
import Textbox from "../Textbox";
// Assume other imports for SelectList, UserList etc.

const AddTask = ({ open, setOpen, onSuccess }) => {
  const { register, handleSubmit } = useForm();

  // This function runs when the user submits the form
  const handleOnSubmit = async (data) => {
    console.log('Form data:', data);
    // In a real app, you would send this data to the backend API here
    // using useCreateTaskMutation from taskApiSlice.js (covered later)
    console.log('Submitting task...');
    // onSuccess(); // Call this if submission was successful
    // setOpen(false); // Close the modal
  };

  return (
    // Modal wrapper or form layout goes here
    <form onSubmit={handleSubmit(handleOnSubmit)}>
      {/* Basic Task Details */}
      <Textbox label="Task Title" {...register("title")} />
      <textarea placeholder="Description" {...register("description")} />

      {/* Other Fields (Simplified representation) */}
      <div> {/* Team Selection (UserList component) */} </div>
      <div> {/* Stage Selection (SelectList component) */} </div>
      <div> {/* Priority Selection (SelectList component) */} </div>
      <div> {/* Date Picker (Input type date) */} </div>
      <div> {/* Assets/File Upload area */} </div>
      <div> {/* Parent Task selection */} </div>


      {/* Submit Button */}
      <Button label="Create Task" type="submit" />
    </form>
  );
};

export default AddTask;
```
*   This simplified code shows the essential part: a form using `react-hook-form` to collect input for `title`, `description`, and other fields.
*   When the form is submitted, `handleSubmit` calls `handleOnSubmit`.
*   In a real application, `handleOnSubmit` would then send this `data` to the backend server using an API call ([API Communication (RTK Query)](04_api_communication__rtk_query__.md), specifically the `useCreateTaskMutation` hook from `taskApiSlice.js`).
*   The `onSuccess` function would be called if the backend successfully created the task, often triggering a refresh of the task list. `setOpen(false)` would close the form modal.

This interaction is the user's gateway to adding a task to the system.

### Behind the Scenes: How Task Creation Works

When you fill out the form and click "Create Task," here's a simplified journey of that data:

1.  **Client Sends Data:** The browser sends the task details (title, description, etc.) to a specific location (an **API Endpoint**) on the backend server.
2.  **Server Receives Request:** A piece of code on the server (**Controller**) is waiting to handle requests to create tasks.
3.  **Authentication Check:** Before the task is created, **Authentication Middleware** ([User and Authentication System](01_user_and_authentication_system_.md)) verifies that the user making the request is logged in and authorized. The middleware adds the user's ID (`req.user.userId`) to the request.
4.  **Create Task Object:** The server takes the data from the request, adds the `userId` (from step 3) as the `createdBy` field, sets default values for things like `stage` ("todo") and `progress` (0%), and prepares a new task object.
5.  **Save to Database:** This new task object is then saved into the application's **Database** ([Database Models](07_database_models_.md)).
6.  **Respond to Client:** The server sends a response back to the browser, confirming success or indicating an error.

Here's a simple diagram:

```mermaid
sequenceDiagram
    participant User as User (Browser)
    participant Frontend as Frontend (React App)
    participant AuthMiddleware as Authentication Middleware
    participant BackendAPI as Backend API (Node.js Server)
    participant TaskDB as Database (MongoDB)

    User->>Frontend: Fills form & Clicks "Create Task"
    Frontend->>BackendAPI: POST /api/tasks/create (Task Data)
    BackendAPI->>AuthMiddleware: Request hits middleware first
    AuthMiddleware->>AuthMiddleware: Verify JWT Token
    AuthMiddleware->>BackendAPI: Token OK, Add req.user = { userId, ... }
    BackendAPI->>BackendAPI: Prepare Task Data (Add createdBy = req.user.userId)
    BackendAPI->>TaskDB: Save New Task Document
    TaskDB-->>BackendAPI: Confirmation & New Task ID
    BackendAPI-->>Frontend: Success Response
    Frontend->>User: Show Task List (refreshed)
```
*   The diagram shows how the `AuthMiddleware` is crucial for identifying the user *before* the task controller even runs.
*   The Backend API prepares the data and interacts with the `TaskDB` ([Database Models](07_database_models_.md)).

Let's look at simplified parts of the backend code that handle this flow:

First, the controller function `createTask` from `taskController.js` is responsible for receiving the request and interacting with the database:

```javascript
// server\controllers\taskController.js (Simplified createTask)
import asyncHandler from "express-async-handler";
import Task from "../models/taskModel.js"; // Import Task model

const createTask = asyncHandler(async (req, res) => {
  // req.user is added by the protectRoute middleware
  const { userId } = req.user;
  console.log('User ID from auth middleware:', userId);

  // Get task data from the request body
  const { title, description, team, stage, date, priority, assets, parentId } = req.body;

  // Prepare the data to save to the database
  const taskData = {
    title,
    description: description || "",
    team,
    stage: stage.toLowerCase(),
    date,
    priority: priority.toLowerCase(),
    assets,
    createdBy: userId, // Associate task with the logged-in user
    parent: parentId || null, // Link to parent if exists
  };

  // Save the task to the database
  const task = await Task.create(taskData);
  console.log('New task created in DB with ID:', task._id);

  // Send a success response
  res.status(201).json({
    status: true,
    task,
    message: "Task created successfully.",
  });
});
```
*   The `createTask` function receives the data from the frontend via `req.body`.
*   It gets the ID of the user who is currently logged in from `req.user.userId` (thanks to the authentication middleware - [User and Authentication System](01_user_and_authentication_system_.md)).
*   It creates a `taskData` object, making sure to include the `createdBy` field set to the `userId`.
*   It then uses `Task.create(taskData)` to save this data as a new document (a new row/record) in the tasks collection in our database ([Database Models](07_database_models_.md)).
*   Finally, it sends a success response back to the client.

The `Task` model defines the structure of a task in the database:

```javascript
// server\models\taskModel.js (Simplified)
import mongoose, { Schema } from "mongoose";

const taskSchema = new Schema(
  {
    title: { type: String, required: true },
    description: { type: String, default: "" },
    date: { type: Date, default: new Date() },
    priority: { type: String, default: "normal", enum: ["high", "medium", "normal", "low"] },
    stage: { type: String, default: "todo", enum: ["todo", "in progress", "completed"] },
    progress: { type: Number, default: 0, min: 0, max: 100 },
    team: [{ type: Schema.Types.ObjectId, ref: "User" }], // References User model
    assets: [String], // Array of file URLs
    parent: { type: Schema.Types.ObjectId, ref: "Task", default: null }, // References another Task (parent)
    children: [{ type: Schema.Types.ObjectId, ref: "Task" }], // References other Tasks (children)
    createdBy: { type: Schema.Types.ObjectId, ref: "User" }, // References User model
    isTrashed: { type: Boolean, default: false },
  },
  { timestamps: true } // Adds createdAt and updatedAt
);

const Task = mongoose.model("Task", taskSchema);

export default Task;
```
*   This schema shows the blueprint for a task document in the database.
*   It includes fields like `title`, `description`, `stage`, `priority`, `date`, and `progress`.
*   Crucially, it includes references (`ref`) to other models: `team` and `createdBy` refer to the `User` model ([Database Models](07_database_models_.md)), and `parent` and `children` refer back to the `Task` model itself, enabling task hierarchy.
*   `assets` is an array to store the URLs of attached files.

The API Endpoint definition links the URL path `/api/tasks/create` to the `createTask` controller function, ensuring it's also protected:

```javascript
// server\routes\taskRoute.js (Simplified)
import express from "express";
import { createTask } from "../controllers/taskController.js";
import { protectRoute } from "../middleware/authMiddleware.js"; // Import middleware

const router = express.Router();

// This line connects the URL path and HTTP method to the controller
router.post("/create", protectRoute, createTask); // Ensure it's protected

// ... other task routes ...

export default router;
```
*   This snippet from `taskRoute.js` shows how the `/create` endpoint is defined.
*   Notice `protectRoute` is included *before* `createTask`. This is the middleware from [Chapter 1: User and Authentication System](01_user_and_authentication_system_.md) that ensures only authenticated users can access this route.

### Viewing Tasks

Once tasks are created, users need to see them. The `mytask` app provides different ways to view tasks, like a Board View (Kanban style), a List View (table), and a Tree View (hierarchy).

Fetching these tasks involves another API call, this time using a `GET` request to an endpoint like `/api/tasks`. The backend receives the request, retrieves tasks from the database based on criteria (like which stage they are in or who the user is), and sends the list back to the frontend.

Here's a peek at how the main `Tasks.jsx` page fetches and displays tasks:

```jsx
// client\src\pages\Tasks.jsx (Simplified)
import { useState } from "react";
import { Button, Loading, Tabs, Title } from "../components";
import { BoardView } from "../components/tasks";
import { useGetAllTaskQuery } from "../redux/slices/api/taskApiSlice"; // Hook to fetch data
import { useLanguage } from "../context/LanguageContext";

const Tasks = () => {
  const { t } = useLanguage();
  const [selected, setSelected] = useState(0); // State to switch between views
  const [open, setOpen] = useState(false); // State for AddTask modal

  // Use the RTK Query hook to fetch tasks
  // The query can include parameters like status, search, etc.
  const { data, isLoading, refetch } = useGetAllTaskQuery({}); // Fetch all tasks for now

  // The fetched tasks are available in data?.tasks
  const tasks = data?.tasks || [];

  // Translate tab titles (example)
  const TABS = [
    { title: t("boardView"), icon: null },
    { title: t("listView"), icon: null },
    { title: t("treeView"), icon: null },
  ];

  return isLoading ? (
    <div className='py-10'><Loading /></div>
  ) : (
    <div>
      {/* Header */}
      <div className='flex items-center justify-between mb-6'>
        <Title title={t('tasks')} />
        <Button label={t('createTask')} onClick={() => setOpen(true)} />
      </div>

      {/* Tabs for different views */}
      <Tabs tabs={TABS} setSelected={setSelected}>
        {/* Render the selected view based on 'selected' state */}
        {selected === 0 && (
          <div className="p-6">
            {/* Pass fetched tasks to BoardView component */}
            <BoardView tasks={tasks} onTaskUpdate={refetch} />
          </div>
        )}
        {/* ... other views like Table and TaskTree */}
      </Tabs>

      {/* Add Task Modal (Controlled by 'open' state) */}
      <AddTask open={open} setOpen={setOpen} onSuccess={refetch} />
    </div>
  );
};

export default Tasks;
```
*   This component uses `useGetAllTaskQuery` from `taskApiSlice.js` ([API Communication (RTK Query)](04_api_communication__rtk_query__.md)) to fetch the list of tasks from the backend.
*   `isLoading` helps show a loading state while fetching.
*   The fetched tasks (`data?.tasks`) are then passed to view components like `BoardView`.
*   `refetch` is a function provided by the query hook that can be called to re-fetch the task list (e.g., after creating or updating a task using `onSuccess`).
*   Tabs ([Frontend State Management (Redux Toolkit)](03_frontend_state_management__redux_toolkit__.md) might be involved here) allow switching between different visual representations of the tasks.

### Other Key Features

The Task Management Core encompasses more than just creating and viewing lists. It includes:

*   **Updating Tasks:** Users can change a task's title, description, stage, priority, etc. (e.g., moving it from "todo" to "in progress"). This involves sending updated data to the backend (`useUpdateTaskMutation`).
*   **Deleting Tasks:** Users can remove tasks. This often involves "soft-deleting" (marking as trashed) rather than permanently deleting, and requires specific permissions ([User and Authentication System](01_user_and_authentication_system_.md)). This uses mutations like `useTrashTaskMutation`.
*   **Task Details:** Clicking on a task often opens a detailed view (`TaskDetail.jsx`). This view fetches specific task information (`useGetSingleTaskQuery`) and displays its full description, team members, assets, and activity history.

```jsx
// client\src\pages\TaskDetail.jsx (Simplified)
import { useParams } from "react-router-dom";
import { Loading, Tabs } from "../components";
import { TaskProgress } from "../components/tasks";
import { useGetSingleTaskQuery } from "../redux/slices/api/taskApiSlice"; // Fetch single task
import FileViewer from "../components/FileViewer";

const TaskDetail = () => {
  const { id } = useParams(); // Get task ID from URL
  const [selected, setSelected] = useState(0); // For tabs (details, activity, hierarchy)

  // Fetch data for a single task
  const { data, isLoading } = useGetSingleTaskQuery({ id });
  const task = data?.task; // Access the task data

  if (isLoading) {
    return <Loading />; // Show loading while fetching
  }

  // Simplified tabs structure
  const TABS = [
    { title: "Details", icon: null },
    { title: "Activities", icon: null },
    { title: "Hierarchy", icon: null },
  ];

  return (
    <div className='w-full flex flex-col gap-3 mb-4 overflow-y-hidden'>
      <h1 className='text-2xl text-gray-600 font-bold'>{task?.title}</h1>
      <Tabs tabs={TABS} setSelected={setSelected}>
        {selected === 0 && ( // Task Details Tab
          <div className='flex flex-col md:flex-row gap-8 py-8'>
            <div className='w-full md:w-1/2 space-y-6'>
              {/* Display basic task info */}
              <p>Description: {task?.description}</p>
              <p>Stage: {task?.stage}</p>
              <p>Priority: {task?.priority}</p>
              <p>Due Date: {task?.date}</p>

              {/* Display Progress (using TaskProgress component) */}
              <TaskProgress taskId={task?._id} initialProgress={task?.progress || 0} />

              {/* Display Team Members */}
              <p>Team: {task?.team?.map(m => m.name).join(', ')}</p>
            </div>
            <div className='w-full md:w-1/2 space-y-6'>
              {/* Display Assets (Files) */}
              <p>Assets:</p>
              {task?.assets?.length > 0 ? (
                task.assets.map((url, index) => (
                  <div key={index}>{url}</div> // Show file URLs
                ))
              ) : (
                <p>No assets attached.</p>
              )}
              {/* FileViewer component would be used here to open files */}
              <FileViewer isOpen={false} setIsOpen={()=>{}} fileUrl={null} fileName={null} />
            </div>
          </div>
        )}
        {/* ... Tabs for Activities and Hierarchy ... */}
      </Tabs>
    </div>
  );
};

export default TaskDetail;
```
*   `TaskDetail.jsx` fetches the specific task using the ID from the URL.
*   It then displays various properties fetched from the backend, including `description`, `stage`, `priority`, `date`, `progress`, `team` members, and `assets`.
*   The `team` and `assets` fields demonstrate how related data (users) and associated data (file URLs) are part of the task object fetched from the backend.
*   The `TaskProgress` component (not shown in detail) would handle updating the task's progress percentage, calling a backend endpoint for that specific action (`useUpdateTaskProgressMutation`).

*   **Task Hierarchy:** The concept of `parent` and `children` tasks allows building project structures. The Tree View (`TaskTree` component) and the Hierarchy tab in Task Details (`TaskHierarchyView` component) visualize these relationships by fetching related tasks from the backend (`useGetTaskHierarchyQuery`).
*   **Subtasks:** While the model has a `subTasks` array for simpler checklists, the `parent`/`children` relationship offers a more robust hierarchy. Creating a subtask in the `AddTask` component (when editing an existing task) might simply add an entry to the `subTasks` array or create a new task linked via the `parent` field. The provided code snippet for `AddTask.jsx` uses a `parentId` field, indicating the application utilizes the parent-child relationship for hierarchy.

These features collectively provide the comprehensive task management capabilities of the `mytask` application.

### Conclusion

In this chapter, we explored the **Task Management Core**, the heart of the `mytask` application. We learned how tasks are created, seeing the flow from the user filling out a form on the frontend to the data being saved in the database via the backend. We also touched upon the various ways tasks are viewed, updated, and organized using concepts like stages, priorities, team members, assets, and hierarchy.

Understanding how task data flows and is structured is essential for building out the rest of the application's features. The next step is to see how we manage this task data efficiently within the frontend application itself, ensuring a smooth and responsive user experience.

In the next chapter, we'll dive into **Frontend State Management (Redux Toolkit)** to see how the frontend keeps track of things like the list of tasks, the details of a single task, and the current logged-in user.

[Frontend State Management (Redux Toolkit)](03_frontend_state_management__redux_toolkit__.md)

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)