# Tutorial: mytask

This project is a **Task Management** application primarily for Istanbul Atlas University members.
It allows users to **create, organize, track, and collaborate** on tasks, including managing subtasks and attachments.
A core feature is its deep **integration with the Atlas University system** for user authentication and synchronizing user data.
The application is built with a **React frontend** utilizing **Redux Toolkit and RTK Query** for state and API management, and a **Node.js backend** interacting with a **MongoDB database**.
It also supports **multiple languages** for broader accessibility within the university.


**Source Repository:** [None](None)

```mermaid
flowchart TD
    A0["User and Authentication System
"]
    A1["Task Management Core
"]
    A2["Frontend State Management (Redux Toolkit)
"]
    A3["API Communication (RTK Query)
"]
    A4["API Endpoints and Controllers (Backend)
"]
    A5["Database Models
"]
    A6["Internationalization (i18n)
"]
    A7["Atlas University Integration
"]
    A3 -- "Calls" --> A4
    A4 -- "Interacts with" --> A5
    A0 -- "Implemented in" --> A4
    A1 -- "Implemented in" --> A4
    A2 -- "Uses" --> A3
    A2 -- "Manages State for" --> A0
    A2 -- "Manages State for" --> A1
    A7 -- "Accessed via" --> A4
    A7 -- "Syncs Data with" --> A5
    A0 -- "Relies on" --> A7
    A6 -- "Provides Translations to" --> A2
    A6 -- "Provides Translations to" --> A4
    A3 -- "Includes Endpoints for" --> A7
```

## Chapters

1. [User and Authentication System
](01_user_and_authentication_system_.md)
2. [Task Management Core
](02_task_management_core_.md)
3. [Frontend State Management (Redux Toolkit)
](03_frontend_state_management__redux_toolkit__.md)
4. [API Communication (RTK Query)
](04_api_communication__rtk_query__.md)
5. [API Endpoints and Controllers (Backend)
](05_api_endpoints_and_controllers__backend__.md)
6. [Atlas University Integration
](06_atlas_university_integration_.md)
7. [Database Models
](07_database_models_.md)
8. [Internationalization (i18n)
](08_internationalization__i18n__.md)


---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)