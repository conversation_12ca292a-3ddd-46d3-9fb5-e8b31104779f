# Atlas Q&A RAG - Dağıtım Kılavuzu

Bu doküman, Atlas Q&A RAG uygulamasını Docker ve Jenkins kullanarak bir production sunucusuna dağıtma sürecini adım adım açıklamaktadır.

## 1. Mi<PERSON><PERSON><PERSON>l Bakış

<PERSON>, Docker Compose tarafından yönetilen tek bir servisten oluşur:
- **`agentic-rag`**: Ana FastAPI backend uygulaması.

Dağıtım süreci, AWS ECR gibi bir container registry'de saklanan önceden oluşturulmuş Docker imajlarına dayanır.

## 2. <PERSON><PERSON>u Gereksinimleri

Dağıtım yapılacak sunucuda aşağıdaki yazılımların kurulu olması gerekmektedir:
- **Docker Engine**: [Docker Engine Kurulumu](https://docs.docker.com/engine/install/)
- **Docker Compose**: [Docker Compose Kurulumu](https://docs.docker.com/compose/install/)

## 3. <PERSON> ile Otomatik Dağıtım

Projenin kök dizininde bulunan `Jenkinsfile`, CI/CD (Sürekli Entegrasyon / Sürekli Dağıtım) sürecini tamamen otomatize etmek için tasarlanmıştır.

### 3.1. Jenkins Pipeline Job'ının Oluşturulması

`Jenkinsfile`'ı kullanabilmek için Jenkins'te doğru türde bir proje oluşturmanız gerekir.

1.  Jenkins ana sayfasından **New Item**'a tıklayın.
2.  Proje adı olarak `atlas-q-a-rag-pipeline` gibi bir isim girin.
3.  Proje türü olarak **Pipeline**'ı seçin ve **OK**'e tıklayın.
4.  Yapılandırma sayfasında **Pipeline** bölümüne gelin.
5.  **Definition** olarak **Pipeline script from SCM** seçeneğini belirleyin.
6.  **SCM** olarak **Git**'i seçin.
7.  **Repository URL** alanına projenizin Bitbucket URL'sini girin.
8.  **Credentials** alanında Bitbucket erişimi için tanımladığınız kimlik bilgisini seçin.
9.  **Script Path** alanının `Jenkinsfile` olarak ayarlandığından emin olun.
10. **Save** diyerek kaydedin.

### 3.2. Jenkins Credential (Kimlik Bilgileri) Kurulumu

Pipeline'ın güvenli bir şekilde çalışabilmesi için API anahtarları ve sunucu erişim anahtarı gibi gizli bilgilerin Jenkins'in kendi kimlik bilgisi yöneticisine eklenmesi gerekir. Bu, sırların kod deposunda açıkça yer almasını engeller.

**Gerekli Credential'lar:**
1.  **API Anahtarları (`Secret text`):**
    *   `openai-api-key-id`
    *   `tavily-api-key-id`
2.  **Sunucu SSH Anahtarı (`SSH Username with private key`):**
    *   `your-ssh-credentials-id` (Bunu `Jenkinsfile`'da belirttiğiniz kendi ID'niz ile değiştirin)

**Adım Adım Kurulum:**

1.  Jenkins ana sayfasından **Manage Jenkins > Credentials > System > Global credentials (unrestricted)** yolunu izleyin.
2.  **Add Credentials**'a tıklayın.

**`Secret text` oluşturmak için:**
*   **Kind:** `Secret text` seçin.
*   **Secret:** İlgili API anahtarını buraya yapıştırın.
*   **ID:** Yukarıda belirtilen ID'lerden birini (`openai-api-key-id` gibi) tam olarak yazın.
*   **Description:** Açıklayıcı bir metin girin (örn: "OpenAI API Key").
*   Bu işlemi iki adet `Secret text` için de tekrarlayın.

**`SSH Username with private key` oluşturmak için:**
*   **Kind:** `SSH Username with private key` seçin.
*   **ID:** `Jenkinsfile`'da `SSH_CREDENTIALS_ID` için belirlediğiniz ID'yi yazın.
*   **Username:** Sunucuya bağlandığınız kullanıcı adını girin (örn: `ubuntu`, `deployer`).
*   **Private Key:** `Enter directly` seçeneğini işaretleyip sunucuya ait özel SSH anahtarınızın (`private key`) içeriğini yapıştırın.

### 3.3. Jenkins Pipeline Akışı

Gerekli kimlik bilgileri kurulduktan sonra, pipeline kod Bitbucket'a push'landığında otomatik olarak çalışır ve aşağıdaki adımları gerçekleştirir:

1.  **Checkout Code:** Projenin kodunu Bitbucket deposundan çeker.
2.  **Login to AWS ECR:** AWS ECR'a (Container Registry) giriş yapar.
3.  **Build & Push to ECR:**
    *   Uygulamanın Docker imajını `docker/Dockerfile.backend-only` dosyasını kullanarak oluşturur.
    *   İmaja, Git commit hash'i ile benzersiz bir etiket (`tag`) atar.
    *   Oluşturulan imajı AWS ECR'a gönderir (`push`).
4.  **Deploy to Production:**
    *   SSH ile production sunucusuna güvenli bir şekilde bağlanır.
    *   `docker-compose.deploy.yml` dosyasını sunucuya kopyalar.
    *   Sunucuda, Jenkins'in kimlik bilgisi yöneticisinden aldığı sırları ve yeni imajın etiketini (`IMAGE_URI`) kullanarak bir `.env` dosyası oluşturur/günceller.
    *   `docker-compose pull` komutuyla yeni imajı sunucuya çeker.
    *   `docker-compose up -d` komutuyla uygulamayı yeni versiyonuyla başlatır.
    *   Eski ve kullanılmayan Docker imajlarını temizler.

## 4. Manuel Dağıtım Adımları

Uygulamayı sunucuya manuel olarak kurmanız veya güncellemeniz gerekirse:

1.  **Sunucuya SSH ile bağlanın:** `ssh your-user@your-server-ip`
2.  **Proje dizinine gidin:** `cd /opt/atlas-rag` (veya `Jenkinsfile`'da belirttiğiniz yol)
3.  **Yapılandırmayı güncelleyin:**
    *   `docker-compose.deploy.yml` dosyasını bu dizine kopyalayın.
    *   Aşağıdaki bölümde belirtilen değişkenleri içeren bir `.env` dosyası oluşturun.
4.  **`.env` dosyasında yeni imaj etiketini ayarlayın:**
    *   `.env` dosyasını düzenleyin ve `IMAGE_URI` değişkenini ECR'daki yeni imajınızın tam adresiyle güncelleyin.
5.  **En son imajı çekin:** `docker-compose -f docker-compose.deploy.yml pull`
6.  **Servisleri yeniden başlatın:** `docker-compose -f docker-compose.deploy.yml up -d`
7.  **Eski imajları temizleyin (isteğe bağlı):** `docker image prune -af`

## 5. Port Yapılandırması

Uygulamanın port yapılandırması `docker-compose.deploy.yml` dosyasından yönetilir.

```yaml
ports:
  - "3820:3820"
```

Bu `"HOST_PORTU:CONTAINER_PORTU"` formatındadır. Bu örnekte, hem sunucunun dış portu hem de container içindeki uygulamanın çalıştığı port **3820** olarak ayarlanmıştır. Bu, yapılandırmayı daha basit ve anlaşılır kılar.

- **HOST_PORTU (3820):** Uygulamaya dışarıdan erişilecek port.
- **CONTAINER_PORTU (3820):** Uygulamanın container içinde dinlediği port. Bu değer, `environment` altındaki `PORT` değişkeni ile aynı olmalıdır.

## 6. Ortam Değişkenleri (`.env` Dosyası)

Otomatik dağıtım sırasında Jenkins bu dosyayı sunucuda kendisi oluşturur. Manuel dağıtım yapıyorsanız, dağıtım dizininizin kökünde (`/opt/atlas-rag/.env`) aşağıdaki içeriğe sahip bir `.env` dosyası oluşturmanız gerekir.

**Bu dosyayı asla Git'e göndermeyin!**

```dotenv
# Production Environment Variables
OPENAI_API_KEY=sk-your-openai-api-key
TAVILY_API_KEY=tvly-your-tavily-api-key
IMAGE_URI=342894954293.dkr.ecr.eu-central-1.amazonaws.com/atlas-q-a-rag:latest
```
