#!/usr/bin/env python3
"""
Test script for tool selection functionality.
"""

import requests
import json


def test_query(bot_name, query):
    """Test a query with a specific bot."""
    url = f"http://localhost:8000/bots/{bot_name}/query"
    payload = {"query": query}

    print(f"\n{'='*60}")
    print(f"Testing Bot: {bot_name}")
    print(f"Query: {query}")
    print(f"{'='*60}")

    try:
        response = requests.post(url, json=payload, timeout=30)

        if response.status_code == 200:
            result = response.json()
            print(f"✅ Success!")
            print(f"Selected Tools: {result.get('selected_tools', [])}")
            print(
                f"Tool Selection Reasoning: {result.get('tool_selection_reasoning', 'N/A')}"
            )

            # Print response preview
            response_text = result.get("response", "")
            if len(response_text) > 200:
                response_text = response_text[:200] + "..."
            print(f"Response Preview: {response_text}")

        else:
            print(f"❌ Error: {response.status_code}")
            print(f"Response: {response.text}")

    except Exception as e:
        print(f"❌ Exception: {str(e)}")


def main():
    """Run tests."""
    print("Testing Tool Selection Functionality")
    print("====================================")

    # Test cases
    test_cases = [
        # SQL Database queries (should select SQLQueryTool)
        ("AdminBot", "veritabanından bilgi getir"),
        ("AdminBot", "sql veritabanındaki tablo isimlerini getir"),
        ("AdminBot", "birimlist tablosundan 5 kullanıcı getir"),
        ("AdminBot", "SqlQueryTool kullanarak bilgi getir"),
        ("AdminBot", "tablo isimlerini getir"),
        ("AdminBot", "çalışan bilgilerini getir"),
        ("AcademicBot", "veritabanından bilgi getir"),
        ("StudentBot", "veritabanından tablo isimlerini getirir misin"),
        # Simple greetings (should select no tools)
        ("AdminBot", "selam nasılsın"),
        ("AdminBot", "merhaba"),
        ("AcademicBot", "hello"),
        # Document searches (should select DocumentSearchTool)
        ("AcademicBot", "document search ile proje bilgisi getir"),
        ("AcademicBot", "döküman ara"),
        # Web searches (should select WebSearchTool)
        ("AdminBot", "web'den bilgi ara"),
        ("SimpleBot", "google'da ara"),
    ]

    for bot_name, query in test_cases:
        test_query(bot_name, query)

    print(f"\n{'='*60}")
    print("Test completed!")


if __name__ == "__main__":
    main()
