# Logging configuration for Agentic RAG System
# This file configures the centralized logging system

# General logging settings
logging:
  # Log directory (relative to project root)
  log_dir: "logs"

  # Default log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
  log_level: "INFO"

  # Maximum size of each log file in MB
  max_file_size_mb: 10

  # Number of backup files to keep
  backup_count: 5

  # Enable console logging (useful for development)
  enable_console: true

  # Enable JSON formatted logs (useful for log analysis tools)
  enable_json_logs: true

# Specific logger configurations
loggers:
  # API request/response logging
  api:
    level: "INFO"
    file: "api.log"
    json_file: "api.json"
    format: "%(asctime)s - API - %(levelname)s - %(message)s"

  # Error-only logging
  errors:
    level: "ERROR"
    file: "errors.log"
    format: "%(asctime)s - ERROR - %(name)s - %(message)s\n%(pathname)s:%(lineno)d in %(funcName)s"

  # Query processing logging
  queries:
    level: "INFO"
    file: "queries.log"
    json_file: "queries.json"
    format: "%(asctime)s - QUERY - %(message)s"

  # Tool execution logging
  tools:
    level: "INFO"
    file: "tools.log"
    json_file: "tools.json"
    format: "%(asctime)s - TOOL - %(message)s"

  # Performance monitoring
  performance:
    level: "INFO"
    file: "performance.log"
    format: "%(asctime)s - PERF - %(message)s"

# Log file descriptions
log_files:
  app.log: "Main application log with all general information"
  app.json: "Main application log in JSON format for analysis"
  api.log: "HTTP API requests and responses"
  api.json: "API logs in JSON format with structured data"
  errors.log: "Error-only log with detailed stack traces"
  queries.log: "Query processing workflow and results"
  queries.json: "Query logs in JSON format with metadata"
  tools.log: "Tool execution details and performance"
  tools.json: "Tool logs in JSON format with execution metrics"
  performance.log: "Performance metrics and timing information"
  simple.log: "🌟 SADE VE OKUNUR LOG - Tüm önemli olayları emoji'ler ve Türkçe açıklamalarla gösterir"

# Log rotation settings
rotation:
  # When to rotate logs (midnight, size-based, etc.)
  when: "midnight"

  # Interval for time-based rotation
  interval: 1

  # Backup count for rotated files
  backup_count: 7

  # Compress old log files
  compress: true

# Development vs Production settings
environments:
  development:
    log_level: "DEBUG"
    enable_console: true
    max_file_size_mb: 5
    backup_count: 3

  production:
    log_level: "INFO"
    enable_console: false
    max_file_size_mb: 50
    backup_count: 10
    compress: true

# Log analysis and monitoring
monitoring:
  # Enable log metrics collection
  enable_metrics: true

  # Log patterns to monitor for alerts
  alert_patterns:
    - "ERROR"
    - "CRITICAL"
    - "Failed to"
    - "Connection refused"
    - "Timeout"

  # Performance thresholds (in seconds)
  performance_thresholds:
    query_processing: 30.0
    tool_execution: 10.0
    api_response: 5.0

# Structured logging fields
structured_fields:
  # Always include these fields in JSON logs
  required:
    - timestamp
    - level
    - logger
    - message
    - module
    - function
    - line

  # Optional fields that may be included
  optional:
    - request_id
    - user_id
    - bot_name
    - query_id
    - execution_id
    - duration
    - error
    - traceback
    - extra_data

# Log cleanup settings
cleanup:
  # Enable automatic log cleanup
  enabled: true

  # Maximum age of log files in days
  max_age_days: 30

  # Maximum total size of all log files in GB
  max_total_size_gb: 5

  # Cleanup schedule (cron format)
  schedule: "0 2 * * *" # Daily at 2 AM
