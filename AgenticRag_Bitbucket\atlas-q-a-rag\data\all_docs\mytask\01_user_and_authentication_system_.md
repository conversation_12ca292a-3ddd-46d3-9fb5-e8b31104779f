# Chapter 1: User and Authentication System

Welcome to the first chapter of the `mytask` project tutorial! We're going to start by understanding the foundation of almost any application that handles personal information or requires different access levels: the **User and Authentication System**.

### What is the User and Authentication System?

Imagine our `mytask` app is like a building where you can organize all your tasks. Before you can enter the building and start managing your tasks, you need to prove you are a registered resident and get a key or ID card. That's what the **User and Authentication System** does!

It's like the **gatekeeper** for our application. Its main jobs are:

1.  **Knowing who you are:** It keeps track of registered users.
2.  **Letting you in:** It verifies your identity when you try to log in (this is **Authentication**).
3.  **Knowing what you can do:** It understands your **Role** (like a regular user who manages their own tasks, or an administrator who can manage other users).
4.  **Keeping your info safe:** It manages your profile and ensures only you (or sometimes an admin) can see/change your personal details.

This system is crucial because it protects your private task list and allows the application to personalize your experience.

### Our First Goal: Logging In

The most basic use case for this system is **logging in**. How does a user prove they are who they say they are and get access to the `mytask` building?

In our project, we have a special way of handling this for users from **Atlas University**. Instead of creating a separate username and password just for `mytask`, users can often log in using their existing Atlas University credentials. This is a common practice called **Single Sign-On** or using an **External Authentication Provider**.

Let's see how this works from the user's perspective and then peek behind the curtain.

### Logging In (The User Experience)

When you open the `mytask` app, you'll see a login screen. This is where you tell the system who you are.

Here's a simplified look at the login form the user sees:

```jsx
// client\src\pages\Login.jsx (Simplified)
import { useForm } from "react-hook-form";
import { Button, Textbox } from "../components";
import { useProcessAtlasLoginMutation } from "../redux/slices/api/atlasServerApiSlice";

const Login = () => {
  const { register, handleSubmit } = useForm();
  const [processAtlasLogin, { isLoading }] = useProcessAtlasLoginMutation();

  // This function runs when the user clicks the login button
  const handleAtlasLogin = async (data) => {
    try {
      // Send username and password to the server for processing
      const response = await processAtlasLogin({
        username: data.username,
        password: data.password,
      }).unwrap();

      // If successful, the server sends back user info and a token
      console.log("Login successful!", response);
      // ... further steps to save user info and navigate ...

    } catch (err) {
      console.error("Login failed:", err);
      // Show an error message to the user
    }
  };

  return (
    <div className="..."> {/* Layout goes here */}
      <form onSubmit={handleSubmit(handleAtlasLogin)}>
        {/* Input field for Username */}
        <Textbox label="Username" {...register("username")} />
        {/* Input field for Password */}
        <Textbox label="Password" type="password" {...register("password")} />
        {/* Login Button */}
        <Button type="submit" label="Login with Atlas" isLoading={isLoading} />
      </form>
    </div>
  );
};

export default Login;
```
*   This code shows the basic structure: a form that captures `username` and `password` using `react-hook-form`.
*   When the form is submitted (`handleSubmit` calls `handleAtlasLogin`), we use something called `useProcessAtlasLoginMutation`. This is part of how our frontend talks to the backend. It sends the username and password to a specific place (an **API Endpoint**) on our server.
*   `isLoading` helps us show a loading spinner while the login request is being processed.
*   If the server responds successfully (`response`), it means the user is authenticated! The frontend then needs to remember this user (using [Frontend State Management (Redux Toolkit)](03_frontend_state_management__redux_toolkit__.md)) and take them to the main part of the app. If there's an error (`catch`), we show a message.

This minimal example shows the crucial part: collecting credentials and sending them off. The complexity lies in *what happens next* on the server.

### Behind the Scenes: How Login Works

Let's trace the path of the username and password after the user clicks the button:

1.  **Client Sends Request:** The browser sends the username and password to the backend server.
2.  **Server Receives Request:** A specific part of the server code (**API Endpoint**) is set up to handle login requests.
3.  **Process Atlas Login:** The server takes the username and password and interacts with the actual Atlas University authentication system (or an intermediate service that does this). *Our server code snippet indicates it's processing a token received from elsewhere, implying a prior step where the client might have received a token after interacting with Atlas directly or via a different server endpoint, but for simplicity, we can think of the server validating Atlas credentials or a token provided from the client after Atlas authentication.*
4.  **Verify Identity:** The server verifies if the provided credentials (or token) are valid according to the Atlas system.
5.  **Find/Sync User in Database:** The server checks if a user with this Atlas identity already exists in *our* `mytask` database.
    *   If yes, it fetches their details.
    *   If no, it might create a new user record in our database using the information from Atlas. This is **Synchronization**.
6.  **Generate Application Token:** If verification is successful, the server creates a special key (a **JWT Token**) that proves this user is logged into *mytask*. This token is sent back to the browser.
7.  **Client Stores Token:** The browser saves this token (often in cookies or local storage). For subsequent requests, the browser automatically includes this token.
8.  **Authentication Middleware:** For almost every other action (like viewing tasks or profile), before the server handles the request, a special piece of code (**Authentication Middleware**) checks if the request includes a valid `mytask` token. If the token is valid, the server knows who the user is and allows the request to proceed. If not, the request is rejected.

Here's a simple diagram illustrating the main steps after the user submits the form, involving the server, database, and the concept of middleware:

```mermaid
sequenceDiagram
    participant User as User (Browser)
    participant Frontend as Frontend (React App)
    participant BackendAPI as Backend API (Node.js Server)
    participant AuthDB as Database (MongoDB)
    participant AuthMiddleware as Authentication Middleware

    User->>Frontend: Enters Username/Password
    Frontend->>BackendAPI: POST /api/atlas-auth/process-login <br> (Username, Password, potentially Atlas Token)
    BackendAPI->>BackendAPI: Validate Credentials/Token (with Atlas system, conceptual)
    BackendAPI->>AuthDB: Find User by Atlas ID or Email
    alt User Exists
        AuthDB-->>BackendAPI: User Data
        BackendAPI->>BackendAPI: Update User Data (Sync)
        BackendAPI->>AuthDB: Save Updated User
        AuthDB-->>BackendAPI: Confirmation
    else User Does Not Exist
        BackendAPI->>BackendAPI: Create New User Data
        BackendAPI->>AuthDB: Save New User
        AuthDB-->>BackendAPI: New User Data
    end
    BackendAPI->>BackendAPI: Generate JWT Token
    BackendAPI-->>Frontend: User Data + JWT Token
    Frontend->>User: Show Dashboard
    Note right of Frontend: Token stored (e.g., Cookie)

    User->>Frontend: View Profile
    Frontend->>BackendAPI: GET /api/users/current (with JWT Token)
    Frontend->>AuthMiddleware: Request hits middleware first
    AuthMiddleware->>AuthMiddleware: Check for JWT Token
    alt Valid Token
        AuthMiddleware->>BackendAPI: Token verified,<br> knows User ID
        BackendAPI->>AuthDB: Fetch User Data by ID
        AuthDB-->>BackendAPI: User Data
        BackendAPI-->>Frontend: User Data
        Frontend->>User: Show Profile
    else Invalid Token
        AuthMiddleware-->>Frontend: Reject Request (401 Unauthorized)
        Frontend->>User: Redirect to Login
    end
```
*   This diagram shows how the frontend and backend collaborate.
*   Importantly, it shows the `AuthMiddleware` which acts as a security check for most requests *after* login.

### Peeking at the Server Code

Let's look at simplified parts of the server code that handle this process.

First, the controller handles the specific login request:

```javascript
// server\controllers\userController.js (Simplified)
import asyncHandler from "express-async-handler";
import User from "../models/userModel.js";
import createJWT from "../utils/index.js";

// This function handles the request from the client
const processAtlasLogin = asyncHandler(async (req, res) => {
  const { token, username } = req.body; // Client sends token and username

  console.log('Processing Atlas login for username:', username);

  // --- Simplified Authentication Step ---
  // In reality, this would involve validating the 'token' with Atlas API
  // or using username/password directly against Atlas if token isn't the flow.
  // We assume the token is valid and contains user info (like GUID).
  const payload = JSON.parse(Buffer.from(token.split('.')[1], 'base64').toString());
  const guid = payload?.Guid; // Get unique Atlas ID from token payload

  if (!guid) {
     // Handle error if no Atlas ID found
     return res.status(400).json({ message: 'Missing Atlas ID in token' });
  }
  console.log('Found Atlas GUID:', guid);
  // --- End Simplified Authentication Step ---

  // Try to find user in our database by Atlas ID
  let user = await User.findOne({ atlasUserId: guid });

  if (user) {
    console.log(`Found existing user: ${user.name}`);
    // Update user data from payload if needed (sync)
    // e.g., user.name = payload.Name || user.name;
    // user.lastLogin = new Date();
    // await user.save();
  } else {
    console.log('No matching user found, creating new user');
    // Create new user from payload data and GUID
    user = await User.create({
        name: payload.Name || 'New Atlas User',
        email: payload.Email || `atlas-${guid.substring(0, 8)}@mytask.app`, // Generate unique email
        password: Math.random().toString(36).slice(-8), // Dummy password
        atlasUserId: guid,
        isExternal: true, // Mark as external user
        lastLogin: new Date()
    });
    console.log(`Created new user with ID: ${user._id}`);
  }

  // Generate our application's JWT token for this user
  createJWT(res, user._id); // _id is the database ID

  // Send user data back to the client (without password)
  user.password = undefined;
  res.status(200).json(user);
});
```
*   The `processAtlasLogin` function receives the login data.
*   The simplified code shows how it extracts the unique Atlas User ID (`guid`) from the provided token (in a real scenario, this step would involve validating the token with the Atlas API).
*   It then searches our `User` database ([Database Models](07_database_models_.md)) using this `atlasUserId`.
*   If the user exists, it potentially updates their information (synchronization).
*   If the user doesn't exist, it creates a new user record in our database, linking it to the `atlasUserId`.
*   Finally, it creates our *own* `mytask` specific JWT token using `createJWT` (which sets a cookie) and sends the user data back to the client.

Next, the **User Model** defines the structure of a user record in our database:

```javascript
// server\models\userModel.js (Simplified)
import mongoose, { Schema } from "mongoose";

const userSchema = new Schema(
  {
    name: { type: String, required: true },
    email: { type: String, required: true, unique: true },
    password: { type: String, required: false }, // Not required for Atlas users
    role: { type: String, default: 'user' },
    isAdmin: { type: Boolean, default: false },
    isActive: { type: Boolean, default: true },
    isExternal: { type: Boolean, default: true }, // Marks users coming from Atlas
    atlasUserId: { type: String, default: null, unique: true }, // Atlas user ID
    lastLogin: { type: Date, default: Date.now },
  },
  { timestamps: true } // Adds createdAt and updatedAt fields
);

// Method to compare passwords (used for traditional login, not Atlas)
userSchema.methods.matchPassword = function (enteredPassword) {
  // ... bcrypt comparison logic ...
  return true; // Simplified
};

const User = mongoose.model("User", userSchema);

export default User;
```
*   This defines what information we store for each user: their name, email, role, etc.
*   Crucially for Atlas integration, it includes `isExternal` and `atlasUserId` fields to identify and link them to their external account.
*   The `password` field is optional because Atlas users authenticate via Atlas, not a password stored in our system.
*   The `matchPassword` method is there for potential traditional login, but not used in the Atlas flow shown.

Finally, the **Authentication Middleware** ensures that users are logged in for protected routes:

```javascript
// server\middleware\authMiddleware.js (Simplified)
import asyncHandler from "express-async-handler";
import jwt from "jsonwebtoken";
import User from "../models/userModel.js";

// This function runs before certain API endpoints
const protectRoute = asyncHandler(async (req, res, next) => {
  console.log('Checking for authentication token...');
  
  let token = req.cookies.token; // Try to get the token from cookies

  if (token) {
    try {
      console.log('Verifying token...');
      // Verify our application's JWT token
      const decodedToken = jwt.verify(token, process.env.JWT_SECRET);

      console.log('Token verified. User ID:', decodedToken.userId);
      
      // Find the user in the database using the ID from the token
      const currentUser = await User.findById(decodedToken.userId).select('-password');

      if (!currentUser) {
         // If user not found (e.g., deleted), reject
         console.error('User not found for token ID');
         return res.status(401).json({ message: "User not found." });
      }

      // Attach user info to the request so later code knows who is logged in
      req.user = {
        userId: currentUser._id.toString(),
        email: currentUser.email,
        isAdmin: currentUser.isAdmin,
        isExternal: currentUser.isExternal
      };
      
      console.log('Authentication successful.');
      next(); // Let the request proceed to the actual API endpoint handler

    } catch (error) {
      console.error('Token verification failed:', error.message);
      // If token is invalid, clear the cookie and reject
      res.cookie("token", "", { httpOnly: true, expires: new Date(0) });
      res.status(401).json({ message: "Not authorized. Invalid token." });
    }
  } else {
    console.log('No token found.');
    // If no token at all, reject
    res.status(401).json({ message: "Not authorized. No token provided." });
  }
});

// This middleware checks if the user is an admin
const isAdminRoute = (req, res, next) => {
  if (req.user && req.user.isAdmin) {
    console.log('User is Admin.');
    next(); // Allow admin to proceed
  } else {
    console.log('User is not Admin. Denied access.');
    res.status(401).json({ message: "Not authorized as admin." });
  }
};

export { isAdminRoute, protectRoute };
```
*   `protectRoute` checks if a valid `mytask` token is present in the request (it looks in cookies first).
*   It uses `jwt.verify` with a secret key (`process.env.JWT_SECRET`) to ensure the token wasn't tampered with and came from our server.
*   If valid, it extracts the user's ID from the token and fetches the user's details from the database.
*   It attaches the `user` object (or a simplified version) to the request (`req.user`). This is super important because the code that runs *after* the middleware needs to know *which* user is making the request (e.g., "get tasks for *this* user").
*   If no token, or an invalid token, it sends a `401 Unauthorized` error.
*   `isAdminRoute` is another middleware that simply checks if the `req.user` object (placed there by `protectRoute`) indicates the user is an administrator. This protects certain parts of the application that only admins should access.

You can see where these middleware functions are used in the **User Routes**:

```javascript
// server\routes\userRoute.js (Simplified)
import express from "express";
import {
  loginUser, // Standard login (not used in Atlas flow shown)
  logoutUser,
  getTeamList, // Get list of users
  getCurrentUser, // Get logged-in user's profile
  // ... other user controller functions ...
  processAtlasLogin, // Handles Atlas login flow
} from "../controllers/userController.js";
import { isAdminRoute, protectRoute } from "../middleware/authMiddleware.js";

const router = express.Router();

// Public routes (no authentication needed)
router.post("/login", loginUser); // Standard login endpoint
router.post("/process-atlas-login", processAtlasLogin); // Endpoint to process Atlas credentials/token
router.post("/logout", logoutUser);

// Protected routes (require a valid token via `protectRoute`)
router.get("/current", protectRoute, getCurrentUser); // Get profile of the currently logged-in user
router.put("/profile", protectRoute, updateUserProfile); // Update profile of the currently logged-in user

// Admin-only routes (require both `protectRoute` AND `isAdminRoute`)
router.route("/:id")
  .put(protectRoute, isAdminRoute, activateUserProfile) // Activate/Deactivate a user
  .delete(protectRoute, isAdminRoute, deleteUserProfile); // Delete a user

router.get("/get-team", protectRoute, getTeamList); // Get list of all users (often admin only or limited)

// ... other routes ...

export default router;
```
*   This code connects the URL paths (like `/api/users/current`) to the controller functions.
*   Notice `protectRoute` is added before `getCurrentUser`, `updateUserProfile`, and `getTeamList`. This means the middleware will run *before* the controller function, ensuring only authenticated users can access these features.
*   `isAdminRoute` is added *after* `protectRoute` for actions like activating/deactivating users or deleting them, ensuring only *authenticated admins* can do those things.

### More User Features

Besides login, the User and Authentication System supports other features:

*   **User Profiles:** Users can view and update their own information (name, title, etc.). This involves fetching their data from the database ([API Endpoints and Controllers (Backend)](05_api_endpoints_and_controllers__backend__.md), [Database Models](07_database_models_.md)) and displaying it on a profile page ([Frontend State Management (Redux Toolkit)](03_frontend_state_management__redux_toolkit__.md), [API Communication (RTK Query)](04_api_communication__rtk_query__.md)).

    ```jsx
    // client\src\pages\Profile.jsx (Simplified)
    import React from 'react';
    import { useSelector } from 'react-redux';
    import { PageContainer, ProDescriptions } from '@ant-design/pro-components';
    import { Avatar, Space, Tag, Typography } from 'antd';
    import { UserOutlined, MailOutlined } from '@ant-design/icons';

    const { Title, Text } = Typography;

    const Profile = () => {
      const { user } = useSelector((state) => state.auth); // Get user data from Redux
      const userInfo = user?.user || user; // Access user details

      // Define the data to display in the profile
      const profileData = [
        { key: 'name', label: 'Ad Soyad', children: userInfo?.name || 'Kullanıcı' },
        { key: 'email', label: 'E-posta', children: userInfo?.email || 'N/A' },
        { key: 'title', label: 'Unvan', children: <Tag>{userInfo?.title || 'User'}</Tag> },
        // ... other fields like department, company ...
      ];

      return (
        <PageContainer>
          {/* Hero Section showing Avatar and basic info */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '24px', marginBottom: '24px' }}>
             <Avatar size={100} icon={<UserOutlined />} src={userInfo?.photoReference} />
             <Space direction="vertical">
                <Title level={3} style={{ margin: 0 }}>{userInfo?.name || 'Kullanıcı'}</Title>
                <Text type="secondary">{userInfo?.title || 'User'}</Text>
             </Space>
          </div>

          {/* Detailed profile information */}
          <ProDescriptions
            title="Personal Information"
            column={2}
            dataSource={profileData}
          />

          {/* ... other sections like stats, activities, security ... */}
        </PageContainer>
      );
    };

    export default Profile;
    ```
    *   This snippet shows how `Profile.jsx` gets the logged-in `user` data from the Redux store ([Frontend State Management (Redux Toolkit)](03_frontend_state_management__redux_toolkit__.md)).
    *   It then uses this data to display information like name, email, title, etc., fetched during the login process and stored in the state.

*   **User Management (Admin):** Administrators have a special view (`/users`) where they can see a list of all users, their roles, and manage them (e.g., activate/deactivate, change role, delete). This feature heavily relies on the concepts of **Roles** and **Admin Routes**.

    ```jsx
    // client\src\pages\Users.jsx (Simplified)
    import { useGetTeamListsQuery } from "../redux/slices/api/userApiSlice";
    import { Loading } from "../components";
    import { getInitials } from "../utils/index";

    const Users = () => {
      // Fetch list of users from the backend
      const { data, isLoading, error } = useGetTeamListsQuery({ page: 1, limit: 10 });

      // The list of users is in data?.users (based on API response structure)
      const users = data?.users || data?.data || data?.results || [];

      if (isLoading) return <Loading />;
      if (error) return <p>Error loading users: {error.message}</p>;

      return (
        <div className='w-full md:px-1 px-0 mb-6'>
          <h2>Team Members</h2>
          <table>
            <thead>
              <tr>
                <th>Full Name</th>
                <th>Title</th>
                <th>Role</th>
                <th>Active</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {users.length > 0 ? (
                users.map((user, index) => (
                  <tr key={index}>
                    <td>
                      <div className='flex items-center gap-3'>
                        <div className='... bg-blue-700 text-white'>{getInitials(user.name)}</div>
                        {user.name}
                      </div>
                    </td>
                    <td>{user.title}</td>
                    <td>{user.role}</td>
                    <td>{user.isActive ? 'Active' : 'Disabled'}</td>
                    <td>{/* Edit/Delete buttons */}</td>
                  </tr>
                ))
              ) : (
                <tr><td colSpan="5" className="py-4 text-center">No team members found</td></tr>
              )}
            </tbody>
          </table>
          {/* Pagination component */}
        </div>
      );
    };

    export default Users;
    ```
    *   This minimal `Users.jsx` shows fetching a list of users using `useGetTeamListsQuery`. This query goes to a backend endpoint that is protected by `protectRoute` and likely `isAdminRoute`.
    *   It then iterates through the fetched `users` data to display them in a table. The actual edit/delete actions would also call backend endpoints protected by admin middleware.

### Conclusion

In this chapter, we explored the **User and Authentication System** as the essential gateway to our `mytask` application. We saw how users log in, particularly via the integrated Atlas University system, and learned about the core concepts of users, authentication, and roles. We took a brief look at how the frontend handles the login form and how the backend processes the request, interacts with the database to find or create users, and issues a security token. We also touched upon how middleware uses this token to protect other parts of the application and how profiles and user management build upon this foundation.

Understanding this system is key because every interaction in the app depends on knowing who is doing what. Now that we know how users get *into* the system, we can start exploring what they actually *do* inside!

In the next chapter, we'll dive into the core functionality of the app: **Task Management**.

[Task Management Core](02_task_management_core_.md)

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)