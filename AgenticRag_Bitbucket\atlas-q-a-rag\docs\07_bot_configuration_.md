# Chapter 7: Bot Configuration

Welcome back! We've come a long way in understanding the pieces that make up the `atlas-q-a-rag` system. We've learned about the central [AgenticRAG System](01_agenticrag_system_.md), the specialized [Bot](02_bot_.md) units, the [Query Router](03_query_router_.md) that decides when to use tools, the [Tool](04_tool_.md)s that fetch external data, the [Agent (LangGraphAgent)](05_agent__langgraphagent__.md) that synthesizes the final answer, and [Memory Management](06_memory_management_.md) for handling conversation history.

But how do we *define* a specific [Bot](02_bot_.md)? How do we tell the system: "This bot is called 'StudentBot', it can search documents and query a student database, here are the prompts it should use, and here are the settings for its AI brain"?

## What Problem Does Bot Configuration Solve?

Imagine you want to have different kinds of bots: one for academic questions, one for administrative tasks, one for general queries. Each needs a different set of tools, perhaps connects to different databases, and might have a slightly different personality defined by its prompts.

How do you tell the system how to build *each* of these distinct bots? You don't want to write complicated code every time you want to create a new type of bot or change an existing one.

The problem is: **How do we easily define and customize the properties, capabilities, and settings for each individual bot in a structured and flexible way?**

This is precisely what **Bot Configuration** is for!

Think of **Bot Configuration** files as the **recipes** or **blueprints** for creating your bots. Each file contains all the necessary instructions for the [AgenticRAG System](01_agenticrag_system_.md) to assemble a specific [Bot](02_bot_.md) with its unique components.

Using configuration files (specifically, YAML files in this project) allows us to:

*   Define multiple bots easily.
*   Change a bot's settings (like which tools it uses or which AI model) without changing the core code.
*   Keep the definition of a bot separate from the system's logic.

## What Does a Bot Configuration File Contain?

Each bot configuration is defined in a separate YAML file, usually located in the `configs` directory. The filename typically matches the bot's name (e.g., `configs/student_bot.yaml` defines the `StudentBot`).

These files follow a specific structure, outlined by the `BotConfig` model in `app/models/bot_config.py`. Let's look at a simplified example (`configs/simple_bot.yaml`) and break down the key sections:

```yaml
# File: configs\simple_bot.yaml (Simplified Example)

# --- Basic Bot Information ---
name: SimpleBot # The unique identifier for this bot
description: A basic AI assistant that can search the web.

# --- Where to find the instructions (Prompts) ---
prompts:
  system_prompt_path: simple_bot/system.txt # Path to the file with core instructions
  query_prompt_path: simple_bot/query.txt   # Path to the file with instructions for handling queries

# --- Which Tools this bot can use and their settings ---
tools:
  - type: WebSearchTool # Define a specific tool
    enabled: true       # Is this tool active for this bot?
    config:             # Tool-specific settings
      max_results: 5    # How many search results to fetch?

# --- Database Connections (Optional) ---
# database: # Uncomment and configure if tools need DB access
#   sql:
#     connection_string: sqlite:///path/to/my/database.db

# --- How the AI Agent is configured ---
agent:
  type: langgraph       # Specifies the underlying agent logic type (usually 'langgraph')
  model: gpt-4o-mini    # The specific AI model to use
  config:               # Agent-specific settings
    temperature: 0.2    # Controls randomness (0.0 is more focused, higher is more creative)

# --- Extra Information (Optional) ---
# metadata:
#   audience: general
#   institution: Atlas University
```

**Explanation of Sections:**

*   **`name`**: A unique string that identifies this specific bot (e.g., "SimpleBot", "AcademicBot"). This is the name you use in the API call (`/bots/{bot_name}/query`).
*   **`description`**: A brief, human-readable summary of what the bot is for.
*   **`prompts`**: This section tells the system *where* to find the text files containing the prompts for this bot. These prompts are crucial for shaping the [Agent (LangGraphAgent)](05_agent__langgraphagent__.md)'s behavior and style.
*   **`tools`**: This is a list where you define *each* [Tool](04_tool_.md) available to this specific bot.
    *   `type`: Specifies which kind of tool it is (e.g., `WebSearchTool`, `DocumentSearchTool`, `SQLQueryTool`).
    *   `enabled`: A boolean (`true` or `false`) to easily switch a tool on or off for this bot without deleting its configuration.
    *   `config`: A dictionary containing settings specifically needed by that type of tool (e.g., `max_results` for `WebSearchTool`, database paths/names for data tools).
*   **`database` (Optional)**: If any of the tools defined in the `tools` list require database access (like `MongoDBQueryTool` or `SQLQueryTool`), their connection details are specified here. This keeps database info separate from tool type.
*   **`agent`**: Configures the core AI logic ([Agent (LangGraphAgent)](05_agent__langgraphagent__.md)) for this bot. You specify the type of agent (`langgraph` is used here), the specific AI `model` to use (e.g., `gpt-4o-mini`), and any model-specific `config` like `temperature`.
*   **`metadata` (Optional)**: Any other information you want to associate with the bot (e.g., target audience, topics covered).

By creating or modifying these YAML files, you are essentially designing or changing the capabilities of a bot.

## How Does the System Use These Files?

The [AgenticRAG System](01_agenticrag_system_.md) reads these configuration files during its startup process. Remember from Chapter 1 and 2, the `_load_bots` method is responsible for this.

```python
# File: app\core\agentic_rag.py (Simplified Startup Snippet)

class AgenticRAG:
    def __init__(self, config_dir: str = "configs", prompts_dir: str = "prompts"):
        # ... initialization ...
        # Creates a ConfigLoader to read the YAML files
        self.config_loader = ConfigLoader(config_dir)
        self.bots: Dict[str, Dict[str, Any]] = {}

        # Load all bot configurations and set them up!
        self._load_bots() # <--- This calls the loading logic

    def _load_bots(self) -> None:
        """Load all bot configurations and initialize components."""
        bot_configs = self.config_loader.get_all_configs() # Reads all YAML files via ConfigLoader

        for bot_name, bot_config in bot_configs.items(): # Loop through each loaded BotConfig
            try:
                # Use the loaded bot_config to initialize components for THIS bot
                tools = self._initialize_tools(bot_config) # Initializes tools using bot_config.tools
                prompts = self._load_prompts(bot_config) # Loads prompts using bot_config.prompts
                query_router = QueryRouter(bot_config, tools) # Initializes router using bot_config and tools
                agent = LangGraphAgent(bot_config.agent, prompts["system"], prompts["query"]) # Initializes agent using bot_config.agent and prompts

                # Store all the pieces for this bot under its name
                self.bots[bot_name] = {
                    "config": bot_config,
                    "tools": tools,
                    "query_router": query_router,
                    "agent": agent,
                }
                logger.info(f"Loaded bot: {bot_name}")
            except Exception as e:
                logger.error(f"Error loading bot {bot_name}: {str(e)}")
```

**Explanation:**

1.  The `AgenticRAG` system creates a `ConfigLoader`.
2.  The `_load_bots` method calls `self.config_loader.get_all_configs()`. This is where the YAML files are read.
3.  The system then *loops through each bot configuration* it found (e.g., `simple_bot.yaml`, `academic_bot.yaml`, etc.).
4.  For *each* configuration (`bot_config`), it uses the details inside (`bot_config.tools`, `bot_config.prompts`, `bot_config.agent`) to set up the specific components for *that* bot: its unique set of initialized [Tool](04_tool_.md) instances, its [Query Router](03_query_router_.md), and its [Agent (LangGraphAgent)](05_agent__langgraphagent__.md).
5.  These components are then stored in the `self.bots` dictionary, accessible by the bot's `name`.

This clearly shows how the configuration files serve as the instruction set for building each individual bot instance.

## Under the Hood: The ConfigLoader

The `ConfigLoader` class (`app/core/config_loader.py`) is a utility specifically designed to handle reading these YAML files and turning them into structured Python objects.

```python
# File: app\core\config_loader.py (Simplified)

import yaml
from typing import Dict, Optional
from pathlib import Path
# Assuming BotConfig is imported from app.models.bot_config

class ConfigLoader:
    def __init__(self, config_dir: str = "configs"):
        self.config_dir = config_dir
        self.configs: Dict[str, BotConfig] = {} # Stores loaded configs as BotConfig objects
        self._load_configs() # Load on initialization

    def _load_configs(self) -> None:
        config_path = Path(self.config_dir)
        if not config_path.exists():
            logger.warning(f"Config directory {self.config_dir} does not exist")
            return

        for file_path in config_path.glob("*.yaml"): # Find all YAML files
            try:
                with open(file_path, "r") as f:
                    config_data = yaml.safe_load(f) # Read the YAML content

                # Use the BotConfig Pydantic model to parse and validate the data
                bot_config = BotConfig(**config_data)
                self.configs[bot_config.name] = bot_config # Store validated config
                logger.info(f"Loaded configuration for bot: {bot_config.name}")
            except Exception as e:
                logger.error(f"Error loading config from {file_path}: {str(e)}")

    def get_all_configs(self) -> Dict[str, BotConfig]:
        return self.configs # Return the stored BotConfig objects

    # ... load_prompt method ...
    # ... reload_configs method ...
```

**Explanation:**

*   `self.configs: Dict[str, BotConfig]`: This dictionary holds the loaded configurations. The key is the bot's name, and the value is an instance of the `BotConfig` class.
*   `_load_configs()`: This method scans the specified `config_dir`, finds all files ending in `.yaml`. For each file:
    *   It reads the content using the `yaml` library.
    *   `bot_config = BotConfig(**config_data)`: This is a key step! It takes the data read from the YAML file and tries to create an instance of the `BotConfig` Pydantic model. Pydantic automatically validates that the YAML data has the correct structure (e.g., `name` is present and a string, `tools` is a list of objects that match the `ToolConfig` structure, etc.). If the structure is wrong, it will raise an error.
    *   The validated `BotConfig` object is stored in `self.configs`.
*   `get_all_configs()`: This method is what the [AgenticRAG System](01_agenticrag_system_.md) calls to get the dictionary of all loaded `BotConfig` objects.

Using Pydantic models like `BotConfig`, `ToolConfig`, `AgentConfig`, etc. (`app/models/bot_config.py`) is important because it ensures that the configuration files are correctly formatted and contain all the necessary information before the system tries to use them.

## Creating a New Bot using Configuration

Let's put this into practice. Suppose you want to create a new bot called "ResearchHelper" that can search both web and academic documents, but doesn't need database access initially.

1.  **Create a new YAML file:** In the `configs` directory, create a file named `researchhelper_bot.yaml`.
2.  **Define the basic info:** Add `name` and `description`.
3.  **Specify Prompts:** Create corresponding prompt files (e.g., `prompts/researchhelper_bot/system.txt`, `prompts/researchhelper_bot/query.txt`) and link them in the `prompts` section.
4.  **Add Tools:** Include `WebSearchTool` and `DocumentSearchTool` in the `tools` list. Set `enabled` to `true` for both. Add the necessary `config` for each tool (like `max_results` for web search, and `collection_name`/`persist_directory` for document search).
5.  **Configure Agent:** Add the `agent` section, specifying the model you want to use and any temperature settings.
6.  **Omit Database:** Since this bot doesn't need database access *yet*, you can simply omit the `database` section or leave it commented out.

Your `configs/researchhelper_bot.yaml` might look something like this:

```yaml
# File: configs\researchhelper_bot.yaml

name: ResearchHelper # Your new bot's unique name
description: A bot designed to help with research by searching web and documents.

prompts:
  system_prompt_path: researchhelper_bot/system.txt
  query_prompt_path: researchhelper_bot/query.txt

tools:
  - type: WebSearchTool
    enabled: true
    config:
      max_results: 7 # Get a few more results for research
      # Add any other web search specific configs here

  - type: DocumentSearchTool
    enabled: true
    config:
      collection_name: research_papers # Name of the document collection
      persist_directory: ./chroma_db/research # Where the documents are stored
      top_k: 8 # Retrieve top 8 relevant documents

agent:
  type: langgraph
  model: gpt-4-turbo # A capable model for research
  config:
    temperature: 0.1 # Keep it focused and factual
    max_tokens: 3000 # Allow longer responses if needed
```

Once you save this file and restart the `atlas-q-a-rag` application, the `ConfigLoader` will find `researchhelper_bot.yaml`, validate it with `BotConfig`, and the [AgenticRAG System](01_agenticrag_system_.md) will initialize a new `ResearchHelper` bot with a `WebSearchTool`, a `DocumentSearchTool`, its specific prompts, and a `gpt-4-turbo` powered [Agent (LangGraphAgent)](05_agent__langgraphagent__.md). You can then query it using the `/bots/ResearchHelper/query` endpoint!

This demonstrates the power of configuration: defining entirely new bots or modifying existing ones becomes a matter of editing simple YAML files, not writing or changing core Python logic.

## Conclusion

In this chapter, we demystified **Bot Configuration**. We learned that YAML files in the `configs` directory serve as the blueprints for each distinct [Bot](02_bot_.md) in the system. These files define the bot's name, description, the specific [Tool](04_tool_.md)s it has access to (along with their settings), any necessary database connections, and the paths to its governing prompts and [Agent (LangGraphAgent)](05_agent__langgraphagent__.md) configurations. The [AgenticRAG System](01_agenticrag_system_.md), aided by the `ConfigLoader`, reads these files at startup to assemble and initialize each bot's components. This configuration-driven approach makes it easy to create, customize, and manage various specialized bots without altering the core system code.

Now that we understand how bots are defined and configured, let's look at how data gets *into* the system so the document-based tools can find it. In the next chapter, we'll explore the **Document Processor**.

[Document Processor](08_document_processor_.md)

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)