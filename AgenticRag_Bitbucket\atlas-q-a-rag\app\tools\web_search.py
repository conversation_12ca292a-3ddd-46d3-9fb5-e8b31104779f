import logging
import os
from typing import Any, Dict

from tavily import <PERSON>ly<PERSON><PERSON>

from app.tools.base import BaseTool
from app.core.logging_helpers import log_tool_execution

logger = logging.getLogger(__name__)


class WebSearchTool(BaseTool):
    """Tool for searching the web using TavilySearch API."""

    def initialize(self) -> None:
        self.api_key = self.config.get("api_key", os.getenv("TAVILY_API_KEY"))
        self.search_depth = self.config.get("search_depth", "basic")
        self.max_results = self.config.get("max_results", 5)
        self.include_domains = self.config.get("include_domains", [])
        self.exclude_domains = self.config.get("exclude_domains", [])
        self.priority_domain = self.config.get("priority_domain")

        # Öncelikli domain varsa include_domains'in başına ekle
        if self.priority_domain:
            if self.include_domains:
                if self.priority_domain not in self.include_domains:
                    self.include_domains.insert(0, self.priority_domain)
            else:
                self.include_domains = [self.priority_domain]

        if not self.api_key:
            logger.error("Tavily API key not provided")
            self.client = None
        else:
            try:
                self.client = TavilyClient(api_key=self.api_key)
                logger.info("Initialized web search tool with TavilySearch API")
            except Exception as e:
                logger.error(f"Error initializing TavilySearch client: {str(e)}")
                self.client = None

    @log_tool_execution
    async def execute(self, query: str, **kwargs) -> Dict[str, Any]:
        if not self.client:
            return {
                "success": False,
                "error": "TavilySearch client not initialized",
                "results": [],
            }

        try:
            max_results = kwargs.get("max_results", self.max_results)
            search_depth = kwargs.get("search_depth", self.search_depth)

            response = self.client.search(
                query=query,
                search_depth=search_depth,
                max_results=max_results,
                include_domains=self.include_domains or None,
                exclude_domains=self.exclude_domains or None,
            )

            results = response.get("results", [])

            return {
                "success": True,
                "query": query,
                "results": results,
                "count": len(results),
            }
        except Exception as e:
            logger.error(f"Error executing web search: {str(e)}")
            return {"success": False, "error": str(e), "results": []}

    @classmethod
    def get_tool_description(cls) -> str:
        return "Searches the web for information using the TavilySearch API."
