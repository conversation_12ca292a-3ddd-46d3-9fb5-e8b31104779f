"""
Logging helper functions and decorators for the Agentic RAG system.
"""

import functools
import time
import traceback
from typing import Any, Callable, Dict, Optional
import logging
from datetime import datetime
import uuid

from .logging_config import (
    get_api_logger,
    get_error_logger,
    get_query_logger,
    get_tool_logger,
    get_simple_logger,
)


def log_api_request(func: Callable) -> Callable:
    """
    Decorator to log API requests and responses.

    Args:
        func: Function to decorate

    Returns:
        Decorated function
    """

    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        api_logger = get_api_logger()
        request_id = str(uuid.uuid4())[:8]
        start_time = time.time()

        # Log request
        api_logger.info(
            f"[{request_id}] API Request: {func.__name__}",
            extra={
                "request_id": request_id,
                "function": func.__name__,
                "args_count": len(args),
                "kwargs_keys": list(kwargs.keys()),
            },
        )

        try:
            result = await func(*args, **kwargs)
            duration = time.time() - start_time

            # Log successful response
            api_logger.info(
                f"[{request_id}] API Response: {func.__name__} - Success ({duration:.3f}s)",
                extra={
                    "request_id": request_id,
                    "function": func.__name__,
                    "duration": duration,
                    "status": "success",
                },
            )

            return result

        except Exception as e:
            duration = time.time() - start_time
            error_logger = get_error_logger()

            # Log error
            error_logger.error(
                f"[{request_id}] API Error: {func.__name__} - {str(e)} ({duration:.3f}s)",
                extra={
                    "request_id": request_id,
                    "function": func.__name__,
                    "duration": duration,
                    "error": str(e),
                    "traceback": traceback.format_exc(),
                },
            )

            raise

    return wrapper


def log_query_processing(func: Callable) -> Callable:
    """
    Decorator to log query processing.

    Args:
        func: Function to decorate

    Returns:
        Decorated function
    """

    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        query_logger = get_query_logger()
        query_id = str(uuid.uuid4())[:8]
        start_time = time.time()

        # Extract query information
        query_text = ""
        bot_name = ""

        # Try to extract query from different argument patterns
        if args:
            if hasattr(args[0], "query"):
                query_text = (
                    args[0].query[:100] + "..."
                    if len(args[0].query) > 100
                    else args[0].query
                )
            elif hasattr(args[0], "bot_name"):
                bot_name = args[0].bot_name

        if "query" in kwargs:
            query_text = (
                kwargs["query"][:100] + "..."
                if len(kwargs["query"]) > 100
                else kwargs["query"]
            )
        if "bot_name" in kwargs:
            bot_name = kwargs["bot_name"]

        # Log query start
        query_logger.info(
            f"[{query_id}] Query Start: Bot={bot_name}, Query='{query_text}'",
            extra={
                "query_id": query_id,
                "bot_name": bot_name,
                "query_text": query_text,
                "function": func.__name__,
            },
        )

        try:
            result = await func(*args, **kwargs)
            duration = time.time() - start_time

            # Log query completion
            query_logger.info(
                f"[{query_id}] Query Complete: Bot={bot_name} ({duration:.3f}s)",
                extra={
                    "query_id": query_id,
                    "bot_name": bot_name,
                    "duration": duration,
                    "status": "success",
                },
            )

            return result

        except Exception as e:
            duration = time.time() - start_time
            error_logger = get_error_logger()

            # Log query error
            error_logger.error(
                f"[{query_id}] Query Error: Bot={bot_name} - {str(e)} ({duration:.3f}s)",
                extra={
                    "query_id": query_id,
                    "bot_name": bot_name,
                    "duration": duration,
                    "error": str(e),
                    "traceback": traceback.format_exc(),
                },
            )

            raise

    return wrapper


def log_tool_execution(func: Callable) -> Callable:
    """
    Decorator to log tool execution.

    Args:
        func: Function to decorate

    Returns:
        Decorated function
    """

    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        tool_logger = get_tool_logger()
        execution_id = str(uuid.uuid4())[:8]
        start_time = time.time()

        # Extract tool information
        tool_name = ""
        query_text = ""

        # Try to extract tool name from self
        if args and hasattr(args[0], "__class__"):
            tool_name = args[0].__class__.__name__

        # Try to extract query
        if "query" in kwargs:
            query_text = (
                kwargs["query"][:50] + "..."
                if len(kwargs["query"]) > 50
                else kwargs["query"]
            )
        elif len(args) > 1 and isinstance(args[1], str):
            query_text = args[1][:50] + "..." if len(args[1]) > 50 else args[1]

        # Log tool execution start
        tool_logger.info(
            f"[{execution_id}] Tool Start: {tool_name} - Query: '{query_text}'",
            extra={
                "execution_id": execution_id,
                "tool_name": tool_name,
                "query_text": query_text,
                "function": func.__name__,
            },
        )

        try:
            result = await func(*args, **kwargs)
            duration = time.time() - start_time

            # Log result summary
            result_summary = ""
            if isinstance(result, dict):
                if "results" in result:
                    result_summary = f"Found {len(result['results'])} results"
                elif "data" in result:
                    result_summary = f"Returned data with {len(result['data'])} items"
                else:
                    result_summary = f"Returned dict with {len(result)} keys"
            elif isinstance(result, list):
                result_summary = f"Returned {len(result)} items"
            elif isinstance(result, str):
                result_summary = f"Returned text ({len(result)} chars)"

            tool_logger.info(
                f"[{execution_id}] Tool Complete: {tool_name} - {result_summary} ({duration:.3f}s)",
                extra={
                    "execution_id": execution_id,
                    "tool_name": tool_name,
                    "duration": duration,
                    "result_summary": result_summary,
                    "status": "success",
                },
            )

            return result

        except Exception as e:
            duration = time.time() - start_time
            error_logger = get_error_logger()

            # Log tool error
            error_logger.error(
                f"[{execution_id}] Tool Error: {tool_name} - {str(e)} ({duration:.3f}s)",
                extra={
                    "execution_id": execution_id,
                    "tool_name": tool_name,
                    "duration": duration,
                    "error": str(e),
                    "traceback": traceback.format_exc(),
                },
            )

            raise

    return wrapper


class QueryLogger:
    """Helper class for logging query-related information."""

    def __init__(self, query_id: str = None, bot_name: str = None):
        """
        Initialize query logger.

        Args:
            query_id: Unique query identifier
            bot_name: Name of the bot processing the query
        """
        self.query_id = query_id or str(uuid.uuid4())[:8]
        self.bot_name = bot_name or "Unknown"
        self.logger = get_query_logger()
        self.start_time = time.time()

    def log_query_start(self, query_text: str, user_id: str = None):
        """Log query start."""
        self.logger.info(
            f"[{self.query_id}] Query Started: Bot={self.bot_name}, User={user_id}, Query='{query_text[:100]}{'...' if len(query_text) > 100 else ''}'",
            extra={
                "query_id": self.query_id,
                "bot_name": self.bot_name,
                "user_id": user_id,
                "query_text": query_text,
                "event": "query_start",
            },
        )

    def log_tool_selection(self, selected_tools: list, reasoning: str = None):
        """Log tool selection."""
        self.logger.info(
            f"[{self.query_id}] Tools Selected: {', '.join(selected_tools)}",
            extra={
                "query_id": self.query_id,
                "bot_name": self.bot_name,
                "selected_tools": selected_tools,
                "reasoning": reasoning,
                "event": "tool_selection",
            },
        )

    def log_tool_result(self, tool_name: str, result_summary: str, duration: float):
        """Log tool execution result."""
        self.logger.info(
            f"[{self.query_id}] Tool Result: {tool_name} - {result_summary} ({duration:.3f}s)",
            extra={
                "query_id": self.query_id,
                "bot_name": self.bot_name,
                "tool_name": tool_name,
                "result_summary": result_summary,
                "duration": duration,
                "event": "tool_result",
            },
        )

    def log_response_generation(self, response_length: int, duration: float):
        """Log response generation."""
        self.logger.info(
            f"[{self.query_id}] Response Generated: {response_length} characters ({duration:.3f}s)",
            extra={
                "query_id": self.query_id,
                "bot_name": self.bot_name,
                "response_length": response_length,
                "duration": duration,
                "event": "response_generation",
            },
        )

    def log_query_complete(self, success: bool = True, error: str = None):
        """Log query completion."""
        total_duration = time.time() - self.start_time

        if success:
            self.logger.info(
                f"[{self.query_id}] Query Completed Successfully: Bot={self.bot_name} (Total: {total_duration:.3f}s)",
                extra={
                    "query_id": self.query_id,
                    "bot_name": self.bot_name,
                    "total_duration": total_duration,
                    "status": "success",
                    "event": "query_complete",
                },
            )
        else:
            error_logger = get_error_logger()
            error_logger.error(
                f"[{self.query_id}] Query Failed: Bot={self.bot_name} - {error} (Total: {total_duration:.3f}s)",
                extra={
                    "query_id": self.query_id,
                    "bot_name": self.bot_name,
                    "total_duration": total_duration,
                    "error": error,
                    "status": "failed",
                    "event": "query_complete",
                },
            )


def log_performance(operation_name: str):
    """
    Context manager for logging performance metrics.

    Args:
        operation_name: Name of the operation being measured
    """

    class PerformanceLogger:
        def __init__(self, name: str):
            self.name = name
            self.logger = logging.getLogger("performance")
            self.start_time = None

        def __enter__(self):
            self.start_time = time.time()
            self.logger.debug(f"Starting {self.name}")
            return self

        def __exit__(self, exc_type, exc_val, exc_tb):
            duration = time.time() - self.start_time
            if exc_type is None:
                self.logger.info(f"Completed {self.name} in {duration:.3f}s")
            else:
                self.logger.warning(
                    f"Failed {self.name} after {duration:.3f}s: {exc_val}"
                )

    return PerformanceLogger(operation_name)


class SimpleLogger:
    """Sade ve okunabilir logging için helper sınıfı."""

    def __init__(self):
        """Initialize simple logger."""
        self.logger = get_simple_logger()

    def system_start(self, host: str, port: int, debug: bool = False):
        """Sistem başlatma log'u."""
        mode = "DEBUG" if debug else "PRODUCTION"
        self.logger.info(f"🚀 SISTEM BAŞLATILDI | {host}:{port} | Mod: {mode}")

    def bot_loaded(self, bot_name: str, tools_count: int):
        """Bot yüklenme log'u."""
        self.logger.info(f"🤖 BOT YÜKLENDİ | {bot_name} | {tools_count} araç")

    def query_received(self, bot_name: str, query: str, user_id: str = None):
        """Sorgu alındı log'u."""
        query_short = query[:50] + "..." if len(query) > 50 else query
        user_info = f" | Kullanıcı: {user_id}" if user_id else ""
        self.logger.info(
            f"❓ SORGU ALINDI | Bot: {bot_name} | '{query_short}'{user_info}"
        )

    def tools_selected(self, bot_name: str, tools: list):
        """Araç seçimi log'u."""
        tools_str = ", ".join(tools) if tools else "Hiçbiri"
        self.logger.info(f"🔧 ARAÇLAR SEÇİLDİ | Bot: {bot_name} | {tools_str}")

    def tool_executed(
        self, tool_name: str, duration: float, success: bool, result_summary: str = ""
    ):
        """Araç çalıştırma log'u."""
        status = "✅ BAŞARILI" if success else "❌ BAŞARISIZ"
        summary = f" | {result_summary}" if result_summary else ""
        self.logger.info(
            f"⚙️ ARAÇ ÇALIŞTIRILDI | {tool_name} | {status} | {duration:.2f}s{summary}"
        )

    def query_completed(
        self, bot_name: str, duration: float, success: bool, response_length: int = 0
    ):
        """Sorgu tamamlanma log'u."""
        status = "✅ BAŞARILI" if success else "❌ BAŞARISIZ"
        length_info = (
            f" | Yanıt: {response_length} karakter" if response_length > 0 else ""
        )
        self.logger.info(
            f"✨ SORGU TAMAMLANDI | Bot: {bot_name} | {status} | {duration:.2f}s{length_info}"
        )

    def error_occurred(self, context: str, error: str, bot_name: str = None):
        """Hata oluşma log'u."""
        bot_info = f" | Bot: {bot_name}" if bot_name else ""
        error_short = error[:100] + "..." if len(error) > 100 else error
        self.logger.info(f"🚨 HATA | {context} | {error_short}{bot_info}")

    def api_request(self, method: str, path: str, status_code: int, duration: float):
        """API istek log'u."""
        status_emoji = (
            "✅"
            if 200 <= status_code < 300
            else "⚠️" if 400 <= status_code < 500 else "❌"
        )
        self.logger.info(
            f"🌐 API İSTEK | {method} {path} | {status_emoji} {status_code} | {duration:.2f}s"
        )

    def memory_cleared(self, bot_name: str, session_id: str):
        """Hafıza temizleme log'u."""
        self.logger.info(
            f"🧹 HAFIZA TEMİZLENDİ | Bot: {bot_name} | Oturum: {session_id}"
        )

    def config_reloaded(self, success: bool, bot_count: int = 0):
        """Konfigürasyon yeniden yükleme log'u."""
        if success:
            self.logger.info(f"🔄 KONFİGÜRASYON YENİLENDİ | {bot_count} bot yüklendi")
        else:
            self.logger.info("🚨 KONFİGÜRASYON YENİLEME BAŞARISIZ")

    def performance_warning(self, operation: str, duration: float, threshold: float):
        """Performans uyarısı log'u."""
        self.logger.info(
            f"⚠️ YAVAŞ İŞLEM | {operation} | {duration:.2f}s (Eşik: {threshold:.2f}s)"
        )

    def daily_summary(
        self, total_queries: int, successful_queries: int, total_errors: int
    ):
        """Günlük özet log'u."""
        success_rate = (
            (successful_queries / total_queries * 100) if total_queries > 0 else 0
        )
        self.logger.info(
            f"📊 GÜNLÜK ÖZET | Toplam Sorgu: {total_queries} | Başarılı: {successful_queries} (%{success_rate:.1f}) | Hata: {total_errors}"
        )


# Global simple logger instance
_simple_logger = None


def get_simple_logger_instance() -> SimpleLogger:
    """Get global simple logger instance."""
    global _simple_logger
    if _simple_logger is None:
        _simple_logger = SimpleLogger()
    return _simple_logger
